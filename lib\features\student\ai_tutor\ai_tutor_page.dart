import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';
import '../ai_tutor/topics_page.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';

class StudentAITutorPage extends StatefulWidget {
  const StudentAITutorPage({super.key});

  @override
  State<StudentAITutorPage> createState() => _StudentAITutorPageState();
}

class _StudentAITutorPageState extends State<StudentAITutorPage>
    with SingleTickerProviderStateMixin {
  List<dynamic> subjects = [];
  bool isLoading = false;

  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    fetchSubjects();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> fetchSubjects() async {
    setState(() => isLoading = true);
    try {
      final response =
          await http.get(Uri.parse("https://sasthra.in/api/subjects"));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data is List) {
          setState(() => subjects = data);
          _controller.forward(); // Start entrance animation
        } else {
          debugPrint("Unexpected response format: $data");
          setState(() => subjects = []);
        }
      } else {
        debugPrint("API error: ${response.statusCode}");
        setState(() => subjects = []);
      }
    } catch (e) {
      debugPrint("Error fetching subjects: $e");
      setState(() => subjects = []);
    } finally {
      setState(() => isLoading = false);
    }
  }

  // Gradient styles
  final Map<String, List<Color>> gradientColors = {
    "Mathematics": [Color.fromARGB(255, 75, 62, 212), Color(0xFF6A7DFF)],
    "Physics": [Color.fromARGB(255, 216, 36, 96), Color(0xFFF06292)],
    "Chemistry": [Color.fromARGB(255, 239, 80, 31), Color(0xFFFF8A65)],
    "Biology": [Color.fromARGB(255, 54, 177, 60), Color(0xFF81C784)],
  };

  // Icons for subjects
  final Map<String, IconData> subjectIcons = {
    "Mathematics": Icons.calculate,
    "Physics": Icons.light,
    "Chemistry": Icons.science,
    "Biology": Icons.biotech,
  };

  Widget buildSubjectCard(dynamic subject, int index) {
    if (subject is! Map) return const SizedBox();

    final subjectName = subject["subject_name"]?.toString() ?? "Unknown Subject";
    final subjectId = subject["subject_id"]?.toString() ?? "";

    final colors = gradientColors[subjectName] ?? [Colors.blueGrey, Colors.indigo];
    final icon = subjectIcons[subjectName] ?? Icons.school;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final animation = CurvedAnimation(
          parent: _controller,
          curve: Interval(
            (0.15 * index).clamp(0.0, 1.0),
            1.0,
            curve: Curves.easeOutBack,
          ),
        );

        final slideOffset = Tween<Offset>(
          begin: Offset(index.isEven ? -1.5 : 1.5, 0),
          end: Offset.zero,
        ).animate(animation);

        final scaleAnim = Tween<double>(begin: 0.8, end: 1.0).animate(animation);

        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: slideOffset,
            child: ScaleTransition(
              scale: scaleAnim,
              child: child,
            ),
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            PageRouteBuilder(
              transitionDuration: const Duration(milliseconds: 700),
              pageBuilder: (_, __, ___) => TopicsPage(
                subjectId: subjectId,
                subjectName: subjectName,
              ),
              transitionsBuilder: (_, anim, __, child) {
                return ScaleTransition(
                  scale: CurvedAnimation(
                    parent: anim,
                    curve: Curves.elasticOut,
                  ),
                  child: child,
                );
              },
            ),
          );
        },
        child: Hero(
          tag: subjectId,
          child: AnimatedScale(
            duration: const Duration(milliseconds: 200),
            scale: 1.0,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: colors,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: colors.last.withOpacity(0.3),
                    blurRadius: 15,
                    spreadRadius: 3,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Moving glow circle
                  Positioned(
                    right: index.isEven ? -30 : null,
                    left: index.isOdd ? -30 : null,
                    top: -20,
                    child: AnimatedContainer(
                      duration: const Duration(seconds: 8),
                      curve: Curves.easeInOut,
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.08),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      children: [
                        Icon(icon, size: 50, color: Colors.white),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Shimmer.fromColors(
                            baseColor: Colors.white,
                            highlightColor: const Color.fromARGB(255, 125, 125, 123),
                            child: Text(
                              subjectName,
                              style: AppTheme.bodyMedium.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 22,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        const Icon(Icons.arrow_forward_ios,
                            color: Colors.white70, size: 18),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget shimmerLoader() {
    return ListView.builder(
      itemCount: 4,
      padding: const EdgeInsets.symmetric(vertical: 20),
      itemBuilder: (_, __) => Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          height: 120,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: '       AI Tutor',
      subtitle: 'Get instant help with your studies',
      child: isLoading
          ? shimmerLoader()
          : subjects.isEmpty
              ? const Center(child: Text("No subjects available"))
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  itemCount: subjects.length,
                  itemBuilder: (context, index) {
                    return buildSubjectCard(subjects[index], index);
                  },
                ),
    );
  }
}
