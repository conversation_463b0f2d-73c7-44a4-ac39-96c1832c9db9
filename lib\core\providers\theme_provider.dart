import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';
import '../services/auth_service.dart';

/// ThemeProvider - Dynamic Role-Based Theme Provider
///
/// English: This provider manages the application theme based on the current user's role.
/// It watches the authentication state and automatically switches themes when the user role changes.
///
/// Tanglish: Inga provider user role based ah theme ah manage pannum.
/// Authentication state ah watch panni, user role change aagum pothu automatically theme change pannum.
///
/// Features:
/// - Automatic theme switching based on user role
/// - Fallback to default theme for unauthenticated users
/// - Support for both light and dark themes with role-specific colors
/// - Real-time theme updates when user role changes
class ThemeNotifier extends ChangeNotifier {
  final AuthService _authService;
  ThemeData _currentTheme;
  ThemeData _currentDarkTheme;
  String? _currentRole;

  ThemeNotifier(this._authService) 
    : _currentTheme = AppTheme.lightTheme,
      _currentDarkTheme = AppTheme.darkTheme {
    // Listen to auth state changes
    _authService.addListener(_onAuthStateChanged);
    // Initialize with current user role
    _updateThemeForCurrentUser();
  }

  /// Get current light theme
  ThemeData get lightTheme => _currentTheme;

  /// Get current dark theme  
  ThemeData get darkTheme => _currentDarkTheme;

  /// Get current user role
  String? get currentRole => _currentRole;

  /// Handle authentication state changes
  void _onAuthStateChanged() {
    _updateThemeForCurrentUser();
  }

  /// Update theme based on current user role
  void _updateThemeForCurrentUser() {
    final newRole = _authService.userRole;
    
    // Only update if role has changed
    if (newRole != _currentRole) {
      _currentRole = newRole;
      
      if (newRole != null && _authService.isAuthenticated) {
        // User is authenticated, use role-specific theme
        _currentTheme = AppTheme.getRoleTheme(newRole);
        _currentDarkTheme = _createRoleDarkTheme(newRole);
      } else {
        // User is not authenticated, use default theme
        _currentTheme = AppTheme.lightTheme;
        _currentDarkTheme = AppTheme.darkTheme;
      }
      
      notifyListeners();
    }
  }

  /// Create role-specific dark theme
  ThemeData _createRoleDarkTheme(String role) {
    final roleColor = AppTheme.getRoleColor(role);
    final roleLightColor = AppTheme.getRoleColorLight(role);

    return AppTheme.darkTheme.copyWith(
      colorScheme: AppTheme.darkTheme.colorScheme.copyWith(
        primary: roleColor,
        primaryContainer: roleLightColor.withOpacity(0.3),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: roleColor,
          foregroundColor: Colors.white,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: roleColor,
          side: BorderSide(color: roleColor),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: roleColor,
        ),
      ),
      inputDecorationTheme: AppTheme.darkTheme.inputDecorationTheme?.copyWith(
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: roleColor, width: 2),
        ),
      ),
      bottomNavigationBarTheme: AppTheme.darkTheme.bottomNavigationBarTheme?.copyWith(
        selectedItemColor: roleColor,
      ),
    );
  }

  /// Manually set theme for a specific role (useful for testing)
  void setThemeForRole(String role) {
    _currentRole = role;
    _currentTheme = AppTheme.getRoleTheme(role);
    _currentDarkTheme = _createRoleDarkTheme(role);
    notifyListeners();
  }

  /// Reset to default theme
  void resetToDefaultTheme() {
    _currentRole = null;
    _currentTheme = AppTheme.lightTheme;
    _currentDarkTheme = AppTheme.darkTheme;
    notifyListeners();
  }

  /// Get role color for current user
  Color getCurrentRoleColor() {
    return _currentRole != null 
        ? AppTheme.getRoleColor(_currentRole!) 
        : AppTheme.primaryColor;
  }

  /// Get role light color for current user
  Color getCurrentRoleLightColor() {
    return _currentRole != null 
        ? AppTheme.getRoleColorLight(_currentRole!) 
        : const Color(0xFFEEF2FF);
  }

  /// Get role gradient for current user
  LinearGradient getCurrentRoleGradient() {
    return _currentRole != null 
        ? AppTheme.getRoleGradient(_currentRole!) 
        : AppTheme.primaryGradient;
  }

  @override
  void dispose() {
    _authService.removeListener(_onAuthStateChanged);
    super.dispose();
  }
}

/// Provider for theme management
final themeProvider = ChangeNotifierProvider<ThemeNotifier>((ref) {
  final authService = AuthService();
  return ThemeNotifier(authService);
});

/// Provider for current role color
final currentRoleColorProvider = Provider<Color>((ref) {
  final themeNotifier = ref.watch(themeProvider);
  return themeNotifier.getCurrentRoleColor();
});

/// Provider for current role light color
final currentRoleLightColorProvider = Provider<Color>((ref) {
  final themeNotifier = ref.watch(themeProvider);
  return themeNotifier.getCurrentRoleLightColor();
});

/// Provider for current role gradient
final currentRoleGradientProvider = Provider<LinearGradient>((ref) {
  final themeNotifier = ref.watch(themeProvider);
  return themeNotifier.getCurrentRoleGradient();
});

/// Provider for current user role
final currentUserRoleProvider = Provider<String?>((ref) {
  final themeNotifier = ref.watch(themeProvider);
  return themeNotifier.currentRole;
});
