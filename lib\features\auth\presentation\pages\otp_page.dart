library;

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/auth_models.dart';
import '../../models/otp_model.dart';
import '../../controllers/otp_controller.dart';
import '../../../../core/services/auth_service.dart';

class OtpPage extends StatefulWidget {
  const OtpPage({super.key});

  @override
  State<OtpPage> createState() => _OtpPageState();
}

class _OtpPageState extends State<OtpPage> with TickerProviderStateMixin {
  static const kBrandPrimary = Color(0xFF1E88E5);
  static const kBrandAccent = Color(0xFF4CAF50);
  static const kBackground = Color(0xFFF6F8FB);
  static const kSurface = Colors.white;

  final TextEditingController _otpController = TextEditingController();
  final FocusNode _otpFocusNode = FocusNode();
  final AuthService _authService = AuthService();

  late final OtpModel _model;
  late final OtpController _controller;

  late AnimationController _slideController;
  late AnimationController _shakeController;
  late AnimationController _fadeController;

  double get taglineSize => ScreenUtil().screenWidth >= 600 ? 16.sp : 14.sp;

  @override
  void initState() {
    super.initState();
    _initializeMVC();
    _initializeAnimations();
    _setupControllerListeners();

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: kBrandPrimary,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );
  }

  void _initializeMVC() {
    _model = OtpModel();
    _controller = OtpController(_model);
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 450),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 150), () {
      if (mounted) _fadeController.forward();
    });
  }

  void _setupControllerListeners() {
    _controller.addListener(() {
      if (!mounted) return;
      setState(() {});
      _controller.handleNavigation(context);
      if (_controller.shouldTriggerShakeAnimation) {
        _shakeController.forward().then((_) => _shakeController.reset());
        if (_controller.effectiveErrorMessage != null) {
          _showToast(_controller.effectiveErrorMessage!, isError: true);
        }
      }
    });

    _otpController.addListener(() {
      // Avoid hard-coded index to prevent RangeError
      _controller.onOtpChanged(_otpController.text);
    });
  }

  void _showToast(String message, {bool isError = false}) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: isError ? Colors.red : kBrandPrimary,
      textColor: Colors.white,
      fontSize: 16.sp,
    );
  }

  void _clearOtpFields() {
    _otpController.clear();
    _controller.clearOtpFields();
    _otpFocusNode.requestFocus();
    
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _handleOtpVerification() async {
    final otpText = _otpController.text.trim();
    
    if (otpText.length != 6) {
      _showToast("Please enter a 6-digit OTP", isError: true);
      AppLogger.error('OTP verification failed: Invalid OTP length', {'otp_length': otpText.length});
      return;
    }

    // Loading state is managed by the controller internally
    try {
      AppLogger.info('Attempting OTP verification', {'otp_length': otpText.length});
      
      // Update the controller with current OTP value
      _controller.onOtpChanged(otpText);
      
      // Submit the OTP
      await _controller.onOtpSubmit();
      
      // Check for errors after submission
      if (_controller.effectiveErrorMessage != null) {
        _showToast(_controller.effectiveErrorMessage!, isError: true);
        _clearOtpFields(); // Clear on error
      } else {
        // Check auth state to determine success
        final authState = _controller.authState;
        if (authState == AuthState.authenticated) {
          _showToast("OTP verified successfully!");
          // Navigation will be handled by the controller's navigation logic
        }
      }
      
    } catch (e) {
      AppLogger.error('OTP verification failed', {'error': e.toString()});
      _showToast("Failed to verify OTP. Please try again.", isError: true);
      _clearOtpFields();
    }
  }

  void _handleBackToLogin() {
    AppLogger.userAction('Back to Login button pressed', {});
    try {
      _authService.clearError();
      AppLogger.info('Cleared auth state for navigation to /auth/login');
      context.go('/auth/login');
      AppLogger.info('Navigated to /auth/login successfully');
    } catch (e) {
      AppLogger.error('Navigation to /auth/login failed', {'error': e.toString()});
      Navigator.of(context, rootNavigator: true).pushReplacementNamed('/auth/login');
      AppLogger.info('Fallback: Pushed replacement to /auth/login');
      _showToast("Redirected to login page", isError: false);
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _shakeController.dispose();
    _fadeController.dispose();
    _otpController.dispose();
    _otpFocusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: const Size(360, 640));
    final isTablet = ScreenUtil().screenWidth >= 600;
    final horizontalPad = ScreenUtil().screenWidth < 360 ? 12.w : 20.w;
    final cardMaxWidth = isTablet ? 560.w : math.min(480.w, ScreenUtil().screenWidth - (horizontalPad * 2));

    return Scaffold(
      backgroundColor: kBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.only(bottom: math.max(16.h, MediaQuery.of(context).viewInsets.bottom + 16.h)),
          child: Column(
            children: [
              _buildHeader(),
              SizedBox(height: 16.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: horizontalPad),
                child: Center(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: cardMaxWidth),
                    child: _buildOtpCard(),
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _slideController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.fromLTRB(20.w, 28.h, 20.w, 28.h),
          decoration: const BoxDecoration(
            color: kBrandPrimary,
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(24)),
          ),
          child: Transform.translate(
            offset: Offset(0, 40.h * (1 - _slideController.value)),
            child: Opacity(
              opacity: _slideController.value,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 96.w,
                    height: 96.h,
                    decoration: const BoxDecoration(
                      color: kSurface,
                      shape: BoxShape.circle,
                    ),
                    alignment: Alignment.center,
                    child: Image.asset(
                      'assets/icons/sasthra_logo.png',
                      width: 56.w,
                      height: 56.h,
                      fit: BoxFit.contain,
                      errorBuilder: (_, __, ___) => Icon(
                        Icons.sms_outlined,
                        size: 40.sp,
                        color: kBrandPrimary,
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Verify OTP',
                    textAlign: TextAlign.center,
                    style: AppTheme.headingLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 24.sp,
                    ),
                  ),
                  SizedBox(height: 6.h),
                  Text(
                    'Enter the 6-digit code sent to your\nregistered mobile number',
                    textAlign: TextAlign.center,
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: taglineSize,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOtpCard() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeController.value,
          child: Card(
            elevation: 2,
            color: kSurface,
            shadowColor: Colors.black.withOpacity(0.06),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.r),
              side: BorderSide(color: Colors.black.withOpacity(0.06)),
            ),
            child: Padding(
              padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 16.h),
              child: Column(
                children: [
                  _buildOtpInput(),
                  SizedBox(height: 20.h),
                  SizedBox(
                    width: double.infinity,
                    height: 52.h,
                    child: ElevatedButton(
                      onPressed: _controller.shouldShowLoading || !_controller.isOtpValid
                          ? null
                          : _handleOtpVerification,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kBrandPrimary,
                        disabledBackgroundColor: kBrandPrimary.withOpacity(0.6),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: _controller.shouldShowLoading
                          ? SizedBox(
                              width: 22.w,
                              height: 22.h,
                              child: CircularProgressIndicator(
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                                strokeWidth: 2.w,
                              ),
                            )
                          : Text(
                              'Verify OTP',
                              style: AppTheme.labelLarge.copyWith(
                                color: Colors.white,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  _buildResendSection(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOtpInput() {
    final defaultPinTheme = PinTheme(
      width: 56.w,
      height: 64.h,
      textStyle: AppTheme.headingSmall.copyWith(
        color: AppTheme.textOtp,
        fontWeight: FontWeight.bold,
        fontSize: 22.sp,
      ),
      decoration: BoxDecoration(
        color: kSurface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppTheme.borderColor, width: 2.w),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyWith(
      decoration: BoxDecoration(
        color: kBrandPrimary.withOpacity(0.06),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: kBrandPrimary, width: 2.w),
      ),
    );

    final errorPinTheme = defaultPinTheme.copyWith(
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppTheme.errorColor, width: 2.w),
      ),
    );

    return AnimatedBuilder(
      animation: _shakeController,
      builder: (context, child) {
        final shakeOffset = 10.w * _shakeController.value * (1 - _shakeController.value) * 4;
        return Transform.translate(
          offset: Offset(shakeOffset, 0),
          child: Pinput(
            controller: _otpController,
            focusNode: _otpFocusNode,
            length: 6,
            defaultPinTheme: defaultPinTheme,
            focusedPinTheme: focusedPinTheme,
            errorPinTheme: errorPinTheme,
            showCursor: true,
            cursor: Container(
              width: 2.w,
              height: 24.h,
              color: kBrandPrimary,
            ),
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            keyboardType: TextInputType.number,
            hapticFeedbackType: HapticFeedbackType.lightImpact,
            animationDuration: const Duration(milliseconds: 180),
            animationCurve: Curves.easeInOut,
            onChanged: (value) {
              AppLogger.info('OTP changed', {'value': value, 'length': value.length});
              _controller.onOtpChanged(value);
              setState(() {});
            },
            onCompleted: (value) {
              AppLogger.info('OTP completed', {'value': value});
              _handleOtpVerification();
            },
          ).animate().fadeIn().slideY(begin: 0.25),
        );
      },
    );
  }

  Widget _buildResendSection() {
    return Column(
      children: [
        TextButton(
          onPressed: _handleBackToLogin,
          style: TextButton.styleFrom(foregroundColor: AppTheme.textSecondary),
          child: Text(
            'Back to Login',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontSize: taglineSize,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Text(
        '© 2025 Sasthra. All rights reserved.',
        textAlign: TextAlign.center,
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.textTertiary,
          fontSize: 12.sp,
        ),
      ),
    );
  }
}
