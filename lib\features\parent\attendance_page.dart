import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';
import '../../core/theme/app_theme.dart';
import '../../core/widgets/base_page.dart';
import '../../core/config/app_config.dart';

// Attendance Data Models
class AttendanceRecord {
  final String studentName;
  final String status;
  final DateTime date;
  final String time;
  final String course;
  final bool isLate;

  AttendanceRecord({
    required this.studentName,
    required this.status,
    required this.date,
    required this.time,
    required this.course,
    required this.isLate,
  });

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) {
    return AttendanceRecord(
      studentName: json['student_name'] ?? 'Unknown',
      status: json['status'] ?? 'Unknown',
      date: DateTime.tryParse(json['date'] ?? '') ?? DateTime.now(),
      time: json['time'] ?? 'Unknown',
      course: json['course'] ?? 'Unknown',
      isLate: json['is_late'] ?? false,
    );
  }
}

class AttendanceSummary {
  final int totalPresent;
  final int totalAbsent;
  final int lateArrivals;
  final String progressStatus;

  AttendanceSummary({
    required this.totalPresent,
    required this.totalAbsent,
    required this.lateArrivals,
    required this.progressStatus,
  });

  factory AttendanceSummary.fromJson(Map<String, dynamic> json) {
    return AttendanceSummary(
      totalPresent: json['total_present'] ?? 0,
      totalAbsent: json['total_absent'] ?? 0,
      lateArrivals: json['late_arrivals'] ?? 0,
      progressStatus: json['progress_status'] ?? 'Getting Better',
    );
  }
}

// Attendance Provider for state management
class AttendanceProvider with ChangeNotifier {
  List<AttendanceRecord> _attendanceRecords = [];
  AttendanceSummary? _summary;
  bool _isLoading = true;
  bool _isError = false;
  String _errorMessage = '';
  String _selectedView = 'Daily View';
  String _selectedStatus = 'All Statuses';
  String _selectedMonth = DateFormat('MMMM').format(DateTime.now());

  List<AttendanceRecord> get attendanceRecords => _attendanceRecords;
  AttendanceSummary? get summary => _summary;
  bool get isLoading => _isLoading;
  bool get isError => _isError;
  String get errorMessage => _errorMessage;
  String get selectedView => _selectedView;
  String get selectedStatus => _selectedStatus;
  String get selectedMonth => _selectedMonth;

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  Future<void> fetchAttendanceData() async {
    _setLoading(true);
    _setError(false, '');

    try {
      AppLogger.info('Fetching student attendance data for parent panel');

      final currentToken = await _tokenService.getToken();
      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        AppLogger.warning('Token validation failed for attendance API');
        _setError(true, 'Authentication token has expired. Please log in again.');
        return;
      }

      AppLogger.info('Making API call to: ${AppConfig.baseUrl}/student-attendance-parentpanel');

      final response = await _apiService.get(
        '/student-attendance-parentpanel',
        headers: {
          'Authorization': 'Bearer $currentToken',
          'Content-Type': 'application/json',
        },
      );

      AppLogger.info('Attendance API response received: ${response.toString()}');

      // Parse the response data
      final data = response.data;
      if (data != null) {
        // Parse summary data
        if (data['summary'] != null) {
          _summary = AttendanceSummary.fromJson(data['summary']);
          AppLogger.info('Attendance summary parsed: Present=${_summary!.totalPresent}, Absent=${_summary!.totalAbsent}');
        }

        // Parse attendance records
        if (data['records'] != null && data['records'] is List) {
          _attendanceRecords = (data['records'] as List)
              .map((record) => AttendanceRecord.fromJson(record))
              .toList();
          AppLogger.info('Parsed ${_attendanceRecords.length} attendance records');
        }
      }

      _setLoading(false);
      AppLogger.info('Student attendance data loaded successfully');
    } catch (e) {
      AppLogger.error('Failed to fetch attendance data: $e');
      AppLogger.info('Loading mock attendance data for development');

      // Load mock data for development/testing
      _loadMockData();
      _setLoading(false);

      // Uncomment below for production error handling
      /*
      String msg = 'Unable to fetch attendance details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      _setError(true, msg);
      _setLoading(false);
      */
    }
  }

  Future<void> refreshData() async {
    AppLogger.userAction('Attendance data refresh', {'timestamp': DateTime.now().toIso8601String()});
    await fetchAttendanceData();
  }

  void setView(String view) {
    AppLogger.userAction('Attendance view changed', {'view': view});
    _selectedView = view;
    notifyListeners();
  }

  void setStatusFilter(String status) {
    AppLogger.userAction('Attendance status filter changed', {'status': status});
    _selectedStatus = status;
    notifyListeners();
  }

  void setMonthFilter(String month) {
    AppLogger.userAction('Attendance month filter changed', {'month': month});
    _selectedMonth = month;
    notifyListeners();
  }

  List<AttendanceRecord> get filteredRecords {
    List<AttendanceRecord> filtered = _attendanceRecords;

    // Filter by status
    if (_selectedStatus != 'All Statuses') {
      filtered = filtered.where((record) =>
        record.status.toLowerCase() == _selectedStatus.toLowerCase()).toList();
    }

    return filtered;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool error, String message) {
    _isError = error;
    _errorMessage = message;
    notifyListeners();
  }

  void _loadMockData() {
    // Mock summary data
    _summary = AttendanceSummary(
      totalPresent: 5,
      totalAbsent: 4,
      lateArrivals: 5,
      progressStatus: 'Getting Better',
    );

    // Mock attendance records
    _attendanceRecords = [
      AttendanceRecord(
        studentName: 'charan',
        status: 'Present',
        date: DateTime(2025, 7, 19),
        time: '10:24:24',
        course: 'JEE',
        isLate: true,
      ),
      AttendanceRecord(
        studentName: 'charan',
        status: 'Present',
        date: DateTime(2025, 7, 16),
        time: '11:12:07',
        course: 'JEE',
        isLate: true,
      ),
      AttendanceRecord(
        studentName: 'charan',
        status: 'Present',
        date: DateTime(2025, 7, 15),
        time: '16:05:31',
        course: 'JEE',
        isLate: false,
      ),
      AttendanceRecord(
        studentName: 'charan',
        status: 'Absent',
        date: DateTime(2025, 7, 13),
        time: '',
        course: 'JEE',
        isLate: false,
      ),
      AttendanceRecord(
        studentName: 'charan',
        status: 'Absent',
        date: DateTime(2025, 7, 11),
        time: '',
        course: 'JEE',
        isLate: false,
      ),
      AttendanceRecord(
        studentName: 'charan',
        status: 'Present',
        date: DateTime(2025, 7, 10),
        time: '16:56:41',
        course: 'JEE',
        isLate: true,
      ),
    ];

    AppLogger.info('Mock attendance data loaded: ${_attendanceRecords.length} records');
  }
}

// Main Attendance Page Widget
class AttendancePage extends StatefulWidget {
  const AttendancePage({super.key});

  @override
  State<AttendancePage> createState() => _AttendancePageState();
}

class _AttendancePageState extends State<AttendancePage> {
  late AttendanceProvider _attendanceProvider;

  @override
  void initState() {
    super.initState();
    AppLogger.navigation('Student Attendance', 'Page initialized');
    _attendanceProvider = AttendanceProvider();
    _attendanceProvider.fetchAttendanceData();
  }

  @override
  void dispose() {
    _attendanceProvider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _attendanceProvider,
      child: BasePage(
        title: 'Student Attendance',
        subtitle: 'Track your child\'s attendance details',
        breadcrumbs: const ['Dashboard', 'Parent', 'Attendance'],
        actions: [
          IconButton(
            onPressed: () {
              AppLogger.userAction('Attendance refresh', {'source': 'app_bar'});
              _attendanceProvider.refreshData();
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
        child: Consumer<AttendanceProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return _buildLoadingView();
            }

            if (provider.isError) {
              return _buildErrorView(provider);
            }

            return _buildAttendanceContent(provider);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading attendance data...'),
        ],
      ),
    );
  }

  Widget _buildErrorView(AttendanceProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Error Loading Attendance',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            provider.errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              AppLogger.userAction('Attendance retry', {'error': provider.errorMessage});
              provider.refreshData();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceContent(AttendanceProvider provider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Card with Icon and Title
          _buildHeaderCard(),
          const SizedBox(height: 20),

          // Summary Cards
          if (provider.summary != null) _buildSummaryCards(provider.summary!),
          const SizedBox(height: 20),

          // View Toggle and Filters
          _buildFiltersSection(provider),
          const SizedBox(height: 20),

          // Attendance Table
          _buildAttendanceTable(provider),
        ],
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6366F1).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.calendar_today,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Student Attendance',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Track your child\'s attendance details',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(AttendanceSummary summary) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            title: 'Total Present',
            value: summary.totalPresent.toString(),
            icon: Icons.check_circle,
            color: AppTheme.successColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            title: 'Total Absent',
            value: summary.totalAbsent.toString(),
            icon: Icons.cancel,
            color: AppTheme.errorColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            title: 'Late Arrivals',
            value: summary.lateArrivals.toString(),
            icon: Icons.access_time,
            color: AppTheme.warningColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildProgressCard(summary.progressStatus),
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard(String progressStatus) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(Icons.trending_up, color: AppTheme.successColor, size: 24),
          const SizedBox(height: 8),
          Text(
            progressStatus,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.successColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            'Child\'s Attendance Progress',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(AttendanceProvider provider) {
    return Column(
      children: [
        // View Toggle
        Row(
          children: [
            _buildViewToggle(provider),
            const Spacer(),
            _buildMonthFilter(provider),
          ],
        ),
        const SizedBox(height: 16),

        // Filters Row
        Row(
          children: [
            Expanded(child: _buildStatusFilter(provider)),
            const SizedBox(width: 16),
            _buildMoreFiltersButton(),
          ],
        ),
      ],
    );
  }

  Widget _buildViewToggle(AttendanceProvider provider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleButton(
            'Daily View',
            provider.selectedView == 'Daily View',
            () => provider.setView('Daily View'),
          ),
          _buildToggleButton(
            'Monthly View',
            provider.selectedView == 'Monthly View',
            () => provider.setView('Monthly View'),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusFilter(AttendanceProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: provider.selectedStatus,
          isExpanded: true,
          items: ['All Statuses', 'Present', 'Absent']
              .map((status) => DropdownMenuItem(
                    value: status,
                    child: Text(status),
                  ))
              .toList(),
          onChanged: (value) {
            if (value != null) {
              provider.setStatusFilter(value);
            }
          },
        ),
      ),
    );
  }

  Widget _buildMonthFilter(AttendanceProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.calendar_month, size: 16),
          const SizedBox(width: 8),
          Text(provider.selectedMonth),
        ],
      ),
    );
  }

  Widget _buildMoreFiltersButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.filter_list, size: 16),
          SizedBox(width: 4),
          Text('More Filters'),
        ],
      ),
    );
  }

  Widget _buildAttendanceTable(AttendanceProvider provider) {
    final records = provider.filteredRecords;

    if (records.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: const Column(
          children: [
            Icon(Icons.event_busy, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No Attendance Records',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'No attendance data available for the selected filters.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              children: [
                Expanded(flex: 2, child: Text('Student', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 2, child: Text('Status', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 2, child: Text('Date', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 2, child: Text('Time', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 2, child: Text('Course', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 1, child: Text('Late', style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),

          // Table Rows
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: records.length,
            itemBuilder: (context, index) {
              final record = records[index];
              return _buildTableRow(record, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(AttendanceRecord record, int index) {
    final isEven = index % 2 == 0;
    final statusColor = record.status.toLowerCase() == 'present'
        ? AppTheme.successColor
        : AppTheme.errorColor;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isEven ? Colors.white : Colors.grey.shade50,
      ),
      child: Row(
        children: [
          // Student Name with Avatar
          Expanded(
            flex: 2,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
                  child: Text(
                    record.studentName.isNotEmpty ? record.studentName[0].toUpperCase() : 'S',
                    style: const TextStyle(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    record.studentName,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // Status
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    record.status.toLowerCase() == 'present' ? Icons.check_circle : Icons.cancel,
                    color: statusColor,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    record.status,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Date
          Expanded(
            flex: 2,
            child: Text(
              DateFormat('dd/MM/yyyy').format(record.date),
              style: const TextStyle(fontSize: 14),
            ),
          ),

          // Time
          Expanded(
            flex: 2,
            child: Text(
              record.time,
              style: const TextStyle(fontSize: 14),
            ),
          ),

          // Course
          Expanded(
            flex: 2,
            child: Text(
              record.course,
              style: const TextStyle(fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Late Status
          Expanded(
            flex: 1,
            child: Text(
              record.isLate ? 'Yes' : 'No',
              style: TextStyle(
                fontSize: 14,
                color: record.isLate ? AppTheme.warningColor : Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
