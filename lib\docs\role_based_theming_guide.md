# Role-Based Theming Guide

## Overview
The Sasthra mobile application now supports role-based color theming to provide distinct visual experiences for different user roles. Each role has its own primary color, light variant, and gradient.

## Role Color Palette

| Role | Primary Color | Hex Code | Light Variant | Description |
|------|---------------|----------|---------------|-------------|
| Parent | Turquoise/Cyan | `#10E7DC` | `#7FFDF7` | Calming, nurturing |
| Counselor | Golden Yellow | `#F4C430` | `#FAE68A` | Warm, advisory |
| Trainee | Amber/Orange | `#F59E0B` | `#FDE68A` | Energetic, learning |
| Student | Blue | `#2563EB` | `#93C5FD` | Trustworthy, academic |
| Director | Dark Red/Maroon | `#7D1E1C` | `#DC2626` | Authoritative, leadership |
| Teacher | Navy Blue | `#000080` | `#3B82F6` | Professional, educational |
| Mentor | Purple/Magenta | `#7C007C` | `#C084FC` | Wise, guidance |

## Usage Examples

### 1. Get Role-Specific Colors

```dart
// Get primary color for a role
Color parentColor = AppTheme.getRoleColor('parent');
Color studentColor = AppTheme.getRoleColor('student');

// Get light variant for backgrounds
Color lightParentColor = AppTheme.getRoleColorLight('parent');
Color lightStudentColor = AppTheme.getRoleColorLight('student');
```

### 2. Apply Role-Specific Theme

```dart
// Apply role theme to entire app
MaterialApp(
  theme: AppTheme.getRoleTheme('parent'),
  home: MyHomePage(),
)

// Or use in specific widgets
Theme(
  data: AppTheme.getRoleTheme('student'),
  child: MyWidget(),
)
```

### 3. Use Role Gradients

```dart
// Get role-specific gradient
Container(
  decoration: BoxDecoration(
    gradient: AppTheme.getRoleGradient('director'),
    borderRadius: BorderRadius.circular(12),
  ),
  child: Text('Director Dashboard'),
)

// Use predefined gradients
Container(
  decoration: BoxDecoration(
    gradient: AppTheme.parentGradient,
  ),
  child: Text('Parent Section'),
)
```

### 4. Role Display Names

```dart
// Get formatted role name
String displayName = AppTheme.getRoleDisplayName('center_counselor');
// Returns: "Counselor"

String studentName = AppTheme.getRoleDisplayName('student');
// Returns: "Student"
```

## Implementation Guidelines

### 1. Consistent Usage
- Always use `AppTheme.getRoleColor()` instead of hardcoded colors
- Use role-specific themes for major UI sections
- Apply light variants for backgrounds and containers

### 2. Fallback Handling
- All methods provide fallbacks for unknown roles
- Default to `AppTheme.primaryColor` for unrecognized roles

### 3. Performance Considerations
- Role themes are generated dynamically but can be cached
- Use static gradient constants for frequently used combinations

### 4. Accessibility
- All role colors meet WCAG contrast requirements
- Light variants provide sufficient contrast for text

## Migration Guide

### Updating Existing Code

1. **Replace hardcoded colors:**
```dart
// Before
Container(color: Color(0xFF6366F1))

// After
Container(color: AppTheme.getRoleColor(userRole))
```

2. **Update theme applications:**
```dart
// Before
MaterialApp(theme: AppTheme.lightTheme)

// After
MaterialApp(theme: AppTheme.getRoleTheme(currentUserRole))
```

3. **Use role-aware components:**
```dart
// Before
ElevatedButton(
  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
  child: Text('Action'),
)

// After - automatically uses role color when role theme is applied
ElevatedButton(
  child: Text('Action'),
)
```

## Best Practices

1. **Determine role early:** Get user role during authentication and store globally
2. **Apply theme at app level:** Use role theme in MaterialApp for consistency
3. **Use semantic naming:** Prefer role-based colors over specific color names
4. **Test all roles:** Ensure UI works well with all role color schemes
5. **Consider context:** Use appropriate color intensity (primary vs light) based on UI element importance

## Testing

Use the provided `RoleThemeExample` widget to test all role themes:

```dart
import 'package:sasthra/examples/role_theme_example.dart';

void main() {
  runApp(RoleThemeExample());
}
```

This will show all role colors, gradients, and themed components in action.
