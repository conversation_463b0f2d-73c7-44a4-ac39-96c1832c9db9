// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart';
// import 'dart:ui';
// import '../../core/services/token_service.dart';
// import '../../core/services/api_service.dart';
// import '../../core/utils/logger.dart';

// // Define the primary color for consistency and easy access
// const Color _primaryColor = Color(0xFF6e2fca);
// const Color _backgroundColor = Color(0xFFf0f2f5);
// const Color _cardColor = Colors.white;

// class StudentInfo extends StatefulWidget {
//   const StudentInfo({Key? key}) : super(key: key);

//   @override
//   _StudentInfoState createState() => _StudentInfoState();
// }

// class _StudentInfoState extends State<StudentInfo> {
//   Map<String, dynamic>? student;
//   bool isLoading = true;
//   bool isError = false;
//   String errorMessage = '';

//   final TokenService _tokenService = TokenService();
//   final ApiService _apiService = ApiService();

//   @override
//   void initState() {
//     super.initState();
//     fetchStudentDetails();
//   }

//   Future<void> refreshStudentData() async {
//     await fetchStudentDetails();
//   }

//   Future<void> fetchStudentDetails() async {
//     setState(() {
//       isLoading = true;
//       isError = false;
//     });

//     try {
//       final currentToken = await _tokenService.getToken();

//       if (currentToken == null || !(await _tokenService.isTokenValid())) {
//         setState(() {
//           isError = true;
//           errorMessage =
//               'Authentication token has expired. Please log in again.';
//           isLoading = false;
//         });
//         return;
//       }

//       final data = await _apiService.fetchParentDashboard();
//       final studentData = data['student'] ?? data['students']?.first ?? data;

//       setState(() {
//         student = studentData;
//         isLoading = false;
//       });
//     } catch (e) {
//       String msg = 'Unable to fetch student details. Please try again.';
//       if (e.toString().contains('401')) {
//         msg = 'Session expired. Please log in again.';
//       } else if (e.toString().contains('Network')) {
//         msg = 'Network error. Please check your connection.';
//       }
//       setState(() {
//         isError = true;
//         errorMessage = msg;
//         isLoading = false;
//       });
//     }
//   }

//   String _formatDate(dynamic dob) {
//     if (dob == null || dob.toString().isEmpty) {
//       return 'N/A';
//     }
//     try {
//       DateTime parsed =
//           DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parseUtc(dob);
//       return DateFormat('dd/MM/yyyy').format(parsed.toLocal());
//     } catch (_) {
//       try {
//         DateTime parsed = DateTime.parse(dob);
//         return DateFormat('dd/MM/yyyy').format(parsed);
//       } catch (_) {
//         return 'Invalid Date';
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: _backgroundColor,
//       appBar: AppBar(
//         title: const Text(
//           'Student Dashboard',
//           style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
//         ),
//         backgroundColor: _primaryColor,
//         elevation: 0,
//         centerTitle: true,
//       ),
//       body: _buildContent(context),
//     );
//   }

//   Widget _buildContent(BuildContext context) {
//     if (isLoading) {
//       return Center(
//         child: _buildInfoCard(
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: const [
//               SizedBox(
//                 width: 48,
//                 height: 48,
//                 child: CircularProgressIndicator(
//                   strokeWidth: 3,
//                   valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
//                 ),
//               ),
//               SizedBox(height: 16),
//               Text(
//                 'Loading student information...',
//                 style: TextStyle(
//                   color: _primaryColor,
//                   fontSize: 16,
//                   fontWeight: FontWeight.w500,
//                   decoration: TextDecoration.none,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     }

//     if (isError) {
//       return Center(
//         child: _buildInfoCard(
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               const Text("⚠️", style: TextStyle(fontSize: 40)),
//               const SizedBox(height: 12),
//               const Text(
//                 'Error Loading Student Information',
//                 style: TextStyle(
//                   color: _primaryColor,
//                   fontWeight: FontWeight.w600,
//                   fontSize: 18,
//                   decoration: TextDecoration.none,
//                 ),
//               ),
//               const SizedBox(height: 6),
//               Text(
//                 errorMessage,
//                 style: const TextStyle(
//                   color: Colors.black54,
//                   fontSize: 14,
//                   decoration: TextDecoration.none,
//                 ),
//                 textAlign: TextAlign.center,
//               ),
//               const SizedBox(height: 16),
//               ElevatedButton.icon(
//                 onPressed: refreshStudentData,
//                 icon: const Icon(Icons.refresh, color: Colors.white),
//                 label: const Text(
//                   'Retry',
//                   style: TextStyle(
//                     color: Colors.white,
//                     decoration: TextDecoration.none,
//                   ),
//                 ),
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: _primaryColor,
//                   elevation: 0,
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(12),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     }

//     if (student == null) {
//       return Center(
//         child: _buildInfoCard(
//           child: const Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Text("👤", style: TextStyle(fontSize: 40)),
//               SizedBox(height: 12),
//               Text(
//                 "No student information available",
//                 style: TextStyle(
//                   color: _primaryColor,
//                   fontSize: 16,
//                   decoration: TextDecoration.none,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     }

//     return RefreshIndicator(
//       onRefresh: refreshStudentData,
//       color: _primaryColor,
//       child: SingleChildScrollView(
//         physics: const AlwaysScrollableScrollPhysics(),
//         padding: const EdgeInsets.all(20),
//         child: Column(
//           children: [
//             _buildInfoCard(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   _buildCardTitle('Personal Information', Icons.person_outline),
//                   const SizedBox(height: 16),
//                   _buildInfoItem("Full Name",
//                       "${student!['first_name'] ?? ''} ${student!['last_name'] ?? ''}"),
//                   _buildInfoItem(
//                       "Student ID", student!['id']?.toString() ?? "N/A"),
//                   _buildInfoItem("Date of Birth", _formatDate(student!['dob'])),
//                   _buildInfoItem("Religion", student!['religion'] ?? "N/A"),
//                   _buildInfoItem(
//                       "Email Address", student!['student_email'] ?? "N/A",
//                       icon: Icons.email_outlined),
//                   _buildInfoItem("Phone Number", student!['phone'] ?? "N/A",
//                       icon: Icons.phone_outlined),
//                 ],
//               ),
//             ),
//             const SizedBox(height: 20),
//             _buildInfoCard(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   _buildCardTitle('Academic Records', Icons.school_outlined),
//                   const SizedBox(height: 16),
//                   Row(
//                     children: [
//                       Expanded(
//                         child: _buildVisualMarksCard(
//                             "10th Grade", student!['marks_10th']),
//                       ),
//                       const SizedBox(width: 20),
//                       Expanded(
//                         child: _buildVisualMarksCard(
//                             "12th Grade", student!['marks_12th']),
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//             const SizedBox(height: 20),
//             _buildInfoCard(
//               child: _buildQuoteCard(),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   // --- Helper Widgets with Updated UI ---

//   Widget _buildInfoCard({required Widget child}) {
//     return Card(
//       elevation: 5,
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
//       child: Padding(
//         padding: const EdgeInsets.all(24),
//         child: child,
//       ),
//     );
//   }

//   Widget _buildVisualMarksCard(String title, dynamic marks) {
//     final double value = double.tryParse(marks?.toString() ?? '0') ?? 0.0;
//     final double percentage = value / 100;
//     final String displayValue = value > 0 ? '${value.toInt()}%' : 'N/A';

//     return Column(
//       children: [
//         Text(
//           title,
//           style: TextStyle(
//             fontSize: 14,
//             fontWeight: FontWeight.w600,
//             color: _primaryColor.withOpacity(0.8),
//           ),
//         ),
//         const SizedBox(height: 10),
//         Stack(
//           alignment: Alignment.center,
//           children: [
//             SizedBox(
//               width: 100,
//               height: 100,
//               child: CircularProgressIndicator(
//                 value: percentage > 0 ? percentage : 0,
//                 strokeWidth: 8,
//                 backgroundColor: _primaryColor.withOpacity(0.2),
//                 valueColor: AlwaysStoppedAnimation<Color>(
//                   percentage > 0 ? _primaryColor : Colors.grey,
//                 ),
//               ),
//             ),
//             Text(
//               displayValue,
//               style: const TextStyle(
//                 fontSize: 24,
//                 fontWeight: FontWeight.bold,
//                 color: _primaryColor,
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }

//   Widget _buildQuoteCard() {
//     return const Text(
//       "\"Trust your child's journey, even if it doesn't look like yours.\"",
//       style: TextStyle(
//         fontStyle: FontStyle.italic,
//         color: Colors.black54,
//         fontSize: 16,
//         decoration: TextDecoration.none,
//       ),
//       textAlign: TextAlign.center,
//     );
//   }

//   Widget _buildCardTitle(String title, IconData icon) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Icon(icon, color: _primaryColor),
//         const SizedBox(width: 8),
//         Text(
//           title,
//           style: const TextStyle(
//             color: _primaryColor,
//             fontSize: 20,
//             fontWeight: FontWeight.bold,
//             decoration: TextDecoration.none,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildInfoItem(String label, String value, {IconData? icon}) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 8),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (icon != null) ...[
//             Icon(icon, color: _primaryColor, size: 20),
//             const SizedBox(width: 10),
//           ],
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   label,
//                   style: const TextStyle(
//                     color: Colors.black54,
//                     fontSize: 12,
//                     decoration: TextDecoration.none,
//                   ),
//                 ),
//                 const SizedBox(height: 2),
//                 Text(
//                   value,
//                   style: const TextStyle(
//                     color: Colors.black87,
//                     fontSize: 15,
//                     fontWeight: FontWeight.w500,
//                     decoration: TextDecoration.none,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:ui';
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';

// Define the primary color for consistency and easy access
const Color _primaryColor = Color(0xFF6e2fca);
const Color _backgroundColor = Color(0xFFf0f2f5);
const Color _cardColor = Colors.white;

class StudentInfo extends StatefulWidget {
  const StudentInfo({Key? key}) : super(key: key);

  @override
  _StudentInfoState createState() => _StudentInfoState();
}

class _StudentInfoState extends State<StudentInfo> with SingleTickerProviderStateMixin {
  Map<String, dynamic>? student;
  bool isLoading = true;
  bool isError = false;
  String errorMessage = '';

  late final AnimationController _animationController;
  late final Animation<double> _animation;

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );
    _animationController.forward();
    fetchStudentDetails();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> refreshStudentData() async {
    await fetchStudentDetails();
  }

  Future<void> fetchStudentDetails() async {
    setState(() {
      isLoading = true;
      isError = false;
    });

    try {
      final currentToken = await _tokenService.getToken();

      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        setState(() {
          isError = true;
          errorMessage = 'Authentication token has expired. Please log in again.';
          isLoading = false;
        });
        return;
      }

      final data = await _apiService.fetchParentDashboard();
      final studentData = data['student'] ?? data['students']?.first ?? data;

      setState(() {
        student = studentData;
        isLoading = false;
      });
    } catch (e) {
      String msg = 'Unable to fetch student details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      setState(() {
        isError = true;
        errorMessage = msg;
        isLoading = false;
      });
    }
  }

  String _formatDate(dynamic dob) {
    if (dob == null || dob.toString().isEmpty) {
      return 'N/A';
    }
    try {
      DateTime parsed = DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parseUtc(dob);
      return DateFormat('dd/MM/yyyy').format(parsed.toLocal());
    } catch (_) {
      try {
        DateTime parsed = DateTime.parse(dob);
        return DateFormat('dd/MM/yyyy').format(parsed);
      } catch (_) {
        return 'Invalid Date';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Student Information',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: _primaryColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: _buildContent(context),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (isLoading) {
      return Center(
        child: _buildInfoCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              SizedBox(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(_primaryColor),
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Loading student information...',
                style: TextStyle(
                  color: _primaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (isError) {
      return Center(
        child: _buildInfoCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("⚠️", style: TextStyle(fontSize: 40)),
              const SizedBox(height: 12),
              const Text(
                'Error Loading Student Information',
                style: TextStyle(
                  color: _primaryColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  decoration: TextDecoration.none,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                errorMessage,
                style: const TextStyle(
                  color: Colors.black54,
                  fontSize: 14,
                  decoration: TextDecoration.none,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: refreshStudentData,
                icon: const Icon(Icons.refresh, color: Colors.white),
                label: const Text(
                  'Retry',
                  style: TextStyle(
                    color: Colors.white,
                    decoration: TextDecoration.none,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _primaryColor,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (student == null) {
      return Center(
        child: _buildInfoCard(
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("👤", style: TextStyle(fontSize: 40)),
              SizedBox(height: 12),
              Text(
                "No student information available",
                style: TextStyle(
                  color: _primaryColor,
                  fontSize: 16,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return FadeTransition(
      opacity: _animation,
      child: RefreshIndicator(
        onRefresh: refreshStudentData,
        color: _primaryColor,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              _buildInfoCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCardTitle('Personal Information', Icons.person_outline),
                    const SizedBox(height: 16),
                    _buildInfoItem("Full Name", "${student!['first_name'] ?? ''} ${student!['last_name'] ?? ''}"),
                    _buildInfoItem("Student ID", student!['id']?.toString() ?? "N/A"),
                    _buildInfoItem("Date of Birth", _formatDate(student!['dob'])),
                    _buildInfoItem("Religion", student!['religion'] ?? "N/A"),
                    _buildInfoItem("Email Address", student!['student_email'] ?? "N/A", icon: Icons.email_outlined),
                    _buildInfoItem("Phone Number", student!['phone']?.toString() ?? "N/A", icon: Icons.phone_outlined),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              _buildInfoCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCardTitle('Academic Records', Icons.school_outlined),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildVisualMarksCard("10th Grade", student!['marks_10th']),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: _buildVisualMarksCard("12th Grade", student!['marks_12th']),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              _buildInfoCard(
                child: _buildQuoteCard(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // --- Helper Widgets with Updated UI ---

  Widget _buildInfoCard({required Widget child}) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: child,
      ),
    );
  }

  Color _getMarksColor(double value) {
    if (value < 50) {
      return Colors.red;
    } else if (value >= 50 && value <= 75) {
      return Colors.amber;
    } else {
      return Colors.green;
    }
  }

  Widget _buildVisualMarksCard(String title, dynamic marks) {
    final double value = double.tryParse(marks?.toString() ?? '0') ?? 0.0;
    final double percentage = value / 100;
    final String displayValue = value > 0 ? '${value.toInt()}%' : 'N/A';
    final Color indicatorColor = value > 0 ? _getMarksColor(value) : Colors.grey;

    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: _primaryColor.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 10),
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: 100,
              height: 100,
              child: CircularProgressIndicator(
                value: percentage > 0 ? percentage : 0,
                strokeWidth: 8,
                backgroundColor: indicatorColor.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
              ),
            ),
            Text(
              displayValue,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: indicatorColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuoteCard() {
    return const Text(
      "\"Trust your child's journey, even if it doesn't look like yours.\"",
      style: TextStyle(
        fontStyle: FontStyle.italic,
        color: Colors.black54,
        fontSize: 16,
        decoration: TextDecoration.none,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildCardTitle(String title, IconData icon) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: _primaryColor),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            color: _primaryColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            decoration: TextDecoration.none,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null) ...[
            Icon(icon, color: _primaryColor, size: 20),
            const SizedBox(width: 10),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.black54,
                    fontSize: 12,
                    decoration: TextDecoration.none,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
