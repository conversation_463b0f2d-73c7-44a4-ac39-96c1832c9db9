import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../ai_tutor/pdfviewer_page.dart';
import 'package:flutter/foundation.dart' show debugPrint;


class LanguageSelector extends StatefulWidget {
  final String subjectName;
  final String subTopicId;
  final String subTopicName;

  const LanguageSelector({
    Key? key,
    required this.subjectName,
    required this.subTopicId,
    required this.subTopicName,
  }) : super(key: key);

  @override
  State<LanguageSelector> createState() {
    debugPrint('[LanguageSelector] Creating state for subject: $subjectName, '
        'subTopicId: $subTopicId, subTopicName: $subTopicName');
    return _LanguageSelectorState();
  }
}

class _LanguageSelectorState extends State<LanguageSelector>
    with SingleTickerProviderStateMixin {
  final List<Map<String, dynamic>> languages = [
    {"id": "english", "name": "English", "sub": "English", "country": "GB"},
    {
      "id": "thanglish",
      "name": "Tamil + English",
      "sub": "தமிழ் + English",
      "country": "IN"
    },
    {
      "id": "teluglish",
      "name": "Telugu + English",
      "sub": "తెలుగు + English",
      "country": "IN"
    },
    {
      "id": "kanglish",
      "name": "Kannada + English",
      "sub": "ಕನ್ನಡ + English",
      "country": "IN"
    },
    {
      "id": "manglish",
      "name": "Malayalam + English",
      "sub": "മലയാളം + English",
      "country": "IN"
    },
    {
      "id": "hinglish",
      "name": "Hindi + English",
      "sub": "हिन्दी + English",
      "country": "IN"
    },
  ];

  String? _selectedLanguageId;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _selectedLanguageId = languages.first["id"];
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    debugPrint(
        '[LanguageSelectorState] Initializing with subject: ${widget.subjectName}, '
        'subTopicId: ${widget.subTopicId}, subTopicName: ${widget.subTopicName}');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _handleSelection(String languageId) async {
    setState(() {
      _selectedLanguageId = languageId;
    });
  }

  Future<void> _startLearningSession() async {
    if (_selectedLanguageId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please select a language.")),
      );
      return;
    }

    debugPrint(
        '[LanguageSelectorState] Starting fetchLanguageContent with languageId: $_selectedLanguageId');
    final url = "https://sasthra.in/api/selector-url/${widget.subTopicId}";
    debugPrint('[LanguageSelectorState] Fetching content from API → URL: $url, '
        'Language ID: $_selectedLanguageId, Subject: ${widget.subjectName}');

    try {
      debugPrint('[LanguageSelectorState] Sending GET request to $url');
      final response = await http.get(Uri.parse(url));
      debugPrint(
          '[LanguageSelectorState] Received response: Status Code: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final String processorSelectorUrl =
            data['process_selector_url'] as String;
        final String processorSelectorId =
            data['process_selector_id'].toString();

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PdfViewerPage(
                processorUrl: processorSelectorUrl,
                processSelectorId: processorSelectorId,
                languageId: _selectedLanguageId!,
                subjectName: widget.subjectName,
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Failed to load content: ${response.statusCode}"),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('[LanguageSelectorState] Exception fetching content: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error loading content: $e"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[LanguageSelectorState] Building widget');
    return Scaffold(
      appBar: AppBar(
        title: const Text("Select Language",
            style: TextStyle(color: Colors.white)),
        elevation: 0,
        backgroundColor: const Color(0xFF0D123D), // Updated color
      ),
      body: Container(
        color: const Color(0xFF0D123D),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Select your preferred language for ${widget.subjectName} instruction. The content will be delivered in a mix of your chosen language and English.",
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 24),
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.2, // Adjusted to prevent overflow
                  ),
                  itemCount: languages.length,
                  itemBuilder: (context, index) {
                    final lang = languages[index];
                    final isSelected = lang["id"] == _selectedLanguageId;
                    return _buildLanguageCard(lang, isSelected);
                  },
                ),
              ),
              const SizedBox(height: 24),
              _buildStartButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageCard(Map<String, dynamic> lang, bool isSelected) {
    return GestureDetector(
      onTap: () => _handleSelection(lang["id"] as String),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          color:
              isSelected ? Colors.lightBlue.withOpacity(0.2) : Colors.white10,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? Colors.lightBlueAccent : Colors.white12,
            width: 2,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.lightBlueAccent.withOpacity(0.3),
                    spreadRadius: 2,
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Stack(
          children: [
            if (isSelected)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: RadialGradient(
                      colors: [
                        Colors.lightBlueAccent.withOpacity(0.1),
                        Colors.transparent
                      ],
                      stops: const [0.2, 1.0],
                    ),
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    lang["country"] as String,
                    style: TextStyle(
                      color: isSelected ? Colors.lightBlueAccent : Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    lang["name"] as String,
                    style: TextStyle(
                      color: isSelected ? Colors.lightBlueAccent : Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    lang["sub"] as String,
                    style: TextStyle(
                      color: isSelected
                          ? Colors.lightBlueAccent.withOpacity(0.8)
                          : Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Positioned(
                top: 8,
                right: 8,
                child: Icon(
                  Icons.check_circle,
                  color: Colors.lightBlueAccent,
                  size: 24,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStartButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          colors: [Color(0xFF6B4EE8), Color(0xFF4C3EC8)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: _startLearningSession,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.rocket_launch, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  "Start Learning Session",
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
