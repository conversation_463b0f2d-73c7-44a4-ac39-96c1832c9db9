
import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:livekit_client/livekit_client.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/config/app_config.dart';
import '../../../core/utils/logger.dart';

class IncomingCall extends StatefulWidget {
  final String mentorId;
  final Map<String, dynamic>? incomingCall;
  final bool showVideoCall;
  final Function(String, Map<String, dynamic>?) onCallStatusChange;

  const IncomingCall({
    super.key,
    required this.mentorId,
    this.incomingCall,
    required this.showVideoCall,
    required this.onCallStatusChange,
  });

  @override
  State<IncomingCall> createState() => _IncomingCallState();
}

class _IncomingCallState extends State<IncomingCall> {
  final String apiBaseUrl = AppConfig.baseUrl;
  final String livekitUrl = 'wss://livekit.sasthra.in';

  String callState = 'idle';
  Room? room;
  bool isMuted = false;
  bool isVideoOff = false;
  bool isScreenSharing = false;
  int callDuration = 0;
  String error = '';
  String mediaError = '';
  Timer? callTimer;
  EventsListener<RoomEvent>? roomListener;
  Participant? localParticipant;
  Participant? remoteParticipant;

  @override
  void initState() {
    super.initState();
    if (widget.incomingCall != null && widget.incomingCall!['token'] != null) {
      _initializeCall();
    }
  }

  Future<void> _initializeCall() async {
    try {
      await _requestPermissions();
      await _joinRoom(widget.incomingCall!['token']);
    } catch (e) {
      if (mounted) {
        setState(() {
          error = 'Initialization error: $e';
          callState = 'error';
        });
      }
      AppLogger.error('Initialization failed: $e');
      widget.onCallStatusChange('error', widget.incomingCall);
    }
  }

  Future<void> _requestPermissions() async {
    try {
      final cameraStatus = await Permission.camera.request();
      if (!cameraStatus.isGranted) {
        throw 'Camera permission denied';
      }

      final micStatus = await Permission.microphone.request();
      if (!micStatus.isGranted) {
        throw 'Microphone permission denied';
      }

      AppLogger.info('Permissions granted successfully');
    } catch (e) {
      AppLogger.error('Permission request error: $e');
      rethrow;
    }
  }

  void _startCallTimer() {
    callTimer?.cancel();
    callTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() => callDuration++);
      }
    });
  }

  void _stopCallTimer() {
    callTimer?.cancel();
  }

  String _formatDuration(int seconds) {
    final mins = seconds ~/ 60;
    final secs = seconds % 60;
    return '${mins.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  Future<void> _joinRoom(String token) async {
    if (token.isEmpty) {
      setState(() {
        error = 'Invalid token provided';
        callState = 'error';
      });
      widget.onCallStatusChange('error', widget.incomingCall);
      return;
    }

    setState(() => callState = 'connecting');
    AppLogger.info('Attempting to join LiveKit room with URL: $livekitUrl');

    try {
      room = Room();
      roomListener = room!.createListener();

      await room!.connect(
        livekitUrl,
        token,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
          defaultCameraCaptureOptions: CameraCaptureOptions(
            maxFrameRate: 24,
            params: VideoParametersPresets.h540_169,
          ),
          defaultScreenShareCaptureOptions: ScreenShareCaptureOptions(
            useiOSBroadcastExtension: true,
            params: VideoParametersPresets.screenShareH720FPS15,
          ),
        ),
      );

      if (!mounted || room == null) return;
      AppLogger.info('Connected to LiveKit room successfully');

      setState(() {
        localParticipant = room!.localParticipant;
        if (room!.remoteParticipants.isNotEmpty) {
          remoteParticipant = room!.remoteParticipants.values.first;
        }
      });

      roomListener!
        ..on<RoomDisconnectedEvent>((_) => _handleRoomDisconnect())
        ..on<ParticipantConnectedEvent>(_handleParticipantConnected)
        ..on<ParticipantDisconnectedEvent>(_handleParticipantDisconnected)
        ..on<TrackSubscribedEvent>(_onTrackSubscribed)
        ..on<TrackUnsubscribedEvent>(_onTrackUnsubscribed);

      await room!.localParticipant?.setCameraEnabled(true);
      await room!.localParticipant?.setMicrophoneEnabled(true);

      if (mounted) {
        setState(() => callState = 'connected');
        _startCallTimer();
        widget.onCallStatusChange('connected', widget.incomingCall);
      }

    } catch (e) {
      AppLogger.error('Failed to join room: $e');
      if (mounted) {
        setState(() {
          error = 'Failed to connect: ${e.toString()}';
          callState = 'error';
        });
        widget.onCallStatusChange('error', widget.incomingCall);
      }
    }
  }

  void _handleRoomDisconnect() {
    if (mounted) {
      _endCall(shouldNotifyApi: false);
    }
  }

  void _handleParticipantConnected(ParticipantConnectedEvent event) {
    if (mounted) {
      setState(() => remoteParticipant = event.participant);
    }
  }

  void _handleParticipantDisconnected(ParticipantDisconnectedEvent event) {
    if (mounted) {
      setState(() => remoteParticipant = null);
    }
  }

  void _onTrackSubscribed(TrackSubscribedEvent event) {
    if (mounted) {
      setState(() {});
    }
  }

  void _onTrackUnsubscribed(TrackUnsubscribedEvent event) {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _endCall({bool shouldNotifyApi = true}) async {
    AppLogger.info('Ending call...');

    _stopCallTimer();
    
    if (shouldNotifyApi && widget.incomingCall?['room_name'] != null) {
      try {
        await http.post(
          Uri.parse('$apiBaseUrl/call/end'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ${widget.incomingCall?['token'] ?? ''}',
          },
          body: jsonEncode({
            'room_name': widget.incomingCall!['room_name'],
            'mentor_id': widget.mentorId,
            'action': 'end',
          }),
        ).timeout(const Duration(seconds: 5));
        AppLogger.info('Call ended via API successfully');
      } catch (e) {
        AppLogger.error('Error ending call via API: $e');
      }
    }

    await _cleanup();

    if (mounted) {
      setState(() => callState = 'ended');
      widget.onCallStatusChange('ended', widget.incomingCall);
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _cleanup() async {
    await room?.disconnect();
    await roomListener?.dispose();
    room = null;
    roomListener = null;
    _stopCallTimer();
  }

  Future<void> _toggleMute() async {
    if (room?.localParticipant != null) {
      final newMuteState = !isMuted;
      await room!.localParticipant!.setMicrophoneEnabled(!newMuteState);
      if (mounted) {
        setState(() => isMuted = newMuteState);
      }
    }
  }

  Future<void> _toggleVideo() async {
    if (room?.localParticipant != null) {
      final newVideoState = !isVideoOff;
      await room!.localParticipant!.setCameraEnabled(!newVideoState);
      if (mounted) {
        setState(() => isVideoOff = newVideoState);
      }
    }
  }

  Future<void> _toggleScreenShare() async {
    if (room?.localParticipant != null) {
      final newScreenShareState = !isScreenSharing;
      await room!.localParticipant!.setScreenShareEnabled(newScreenShareState);
      if (mounted) {
        setState(() => isScreenSharing = newScreenShareState);
      }
    }
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showVideoCall || widget.incomingCall == null) return const SizedBox.shrink();

    if (callState == 'connecting') {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (callState == 'error') {
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(error, textAlign: TextAlign.center),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context);
                    }
                  },
                  child: const Text('Go Back'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (callState == 'ended') {
      return const Scaffold(body: Center(child: Text('Call Ended')));
    }

    if (callState == 'connected') {
      return Scaffold(
        body: Column(
          children: [
            // Header
            SafeArea(
              child: Container(
                color: Colors.grey[800],
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Student: ${widget.incomingCall!['student_id']}',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'Duration: ${_formatDuration(callDuration)}',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ),
            if (mediaError.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(mediaError, style: const TextStyle(color: Colors.red)),
              ),
            // Video views
            Expanded(
              child: Stack(
                children: [
                  // Remote video
                  _buildParticipantView(remoteParticipant, isRemote: true),
                  // Local video
                  Positioned(
                    top: 16,
                    right: 16,
                    width: 120,
                    height: 160,
                    child: _buildParticipantView(localParticipant, isRemote: false),
                  ),
                ],
              ),
            ),
            // Controls
            Container(
              color: Colors.grey[800],
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildControlButton(
                    onPressed: _toggleMute,
                    icon: isMuted ? Icons.mic_off : Icons.mic,
                    backgroundColor: isMuted ? Colors.red : Colors.green,
                  ),
                  _buildControlButton(
                    onPressed: _toggleVideo,
                    icon: isVideoOff ? Icons.videocam_off : Icons.videocam,
                    backgroundColor: isVideoOff ? Colors.red : Colors.green,
                  ),
                  _buildControlButton(
                    onPressed: _toggleScreenShare,
                    icon: isScreenSharing ? Icons.stop_screen_share : Icons.screen_share,
                    backgroundColor: isScreenSharing ? Colors.blue : Colors.grey[600]!,
                  ),
                  _buildControlButton(
                    onPressed: () => _endCall(),
                    icon: Icons.call_end,
                    backgroundColor: Colors.red,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return const Scaffold(body: Center(child: Text('Unknown state')));
  }

  Widget _buildParticipantView(Participant? participant, {required bool isRemote}) {
    if (participant == null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Text(
            isRemote ? 'Waiting for student...' : 'No local video',
            style: const TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    final videoTrack = participant.videoTrackPublications.isNotEmpty
        ? participant.videoTrackPublications.first.track as VideoTrack?
        : null;

    if (videoTrack == null || videoTrack.muted) {
      return Container(
        color: Colors.grey[800],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(isRemote ? Icons.person : Icons.videocam_off, color: Colors.white, size: 48),
              const SizedBox(height: 8),
              Text(
                isRemote ? 'Student\'s camera is off' : 'Your camera is off',
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    }

    return VideoTrackRenderer(videoTrack);
  }

  Widget _buildControlButton({required VoidCallback onPressed, required IconData icon, required Color backgroundColor}) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.white),
      ),
    );
  }
}