/// otp_model.dart - OTP Model for MVC Architecture
///
/// English: This model handles the business logic and data management for OTP verification
/// functionality. It manages OTP input validation, countdown timer, resend functionality,
/// loading states, and error handling separate from the UI components. This follows the
/// MVC pattern where the model is responsible for data and business logic.
///
/// Tanglish: OTP verification functionality ku vendiya business logic and data management
/// handle pannum model. OTP input validation, countdown timer, resend functionality,
/// loading states, error handling - ellam UI la irundhu separate aaga inga handle pannum.
/// MVC pattern follow pannitu model data and business logic ku responsible.
///
/// Key Responsibilities:
/// - OTP input validation and management
/// - Countdown timer for resend functionality
/// - Loading state management
/// - Error handling and message management
/// - Integration with authentication service
/// - State change notifications
library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/models/auth_models.dart';
import '../../../core/utils/logger.dart';

/// OtpModel - Business Logic and Data Management for OTP Verification
///
/// English: Model class that handles all OTP-related business logic and data management.
/// Tanglish: OTP related business logic and data management handle panna vendiya model class.
class OtpModel extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  // OTP data
  final List<String> _otpDigits = List.filled(6, '');
  
  // State management
  bool _isLoading = false;
  String? _errorMessage;
  int _resendCountdown = 0;
  Timer? _countdownTimer;
  
  // Constants
  static const int _resendTimeoutSeconds = 60;
  
  // Getters
  List<String> get otpDigits => List.unmodifiable(_otpDigits);
  String get otpValue => _otpDigits.join();
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  int get resendCountdown => _resendCountdown;
  bool get canResend => _resendCountdown == 0;
  bool get isOtpComplete => otpValue.length == 6;

  /// Set complete OTP value (for Pinput widget)
  void setOtpValue(String value) {
    // Clear existing digits
    for (int i = 0; i < _otpDigits.length; i++) {
      _otpDigits[i] = '';
    }
    
    // Set new digits from the value
    for (int i = 0; i < value.length && i < 6; i++) {
      _otpDigits[i] = value[i];
    }
    
    notifyListeners();
  }

  /// Set OTP digit at specific index
  void setOtpDigit(int index, String value) {
    if (index >= 0 && index < 6) {
      _otpDigits[index] = value;
      notifyListeners();
    }
  }
  
  /// Clear all OTP digits
  void clearOtp() {
    for (int i = 0; i < _otpDigits.length; i++) {
      _otpDigits[i] = '';
    }
    notifyListeners();
  }
  
  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String? message) {
    _errorMessage = message;
    notifyListeners();
  }
  
  /// Start resend countdown timer
  void startResendCountdown() {
    _resendCountdown = _resendTimeoutSeconds;
    notifyListeners();
    
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendCountdown > 0) {
        _resendCountdown--;
        notifyListeners();
      } else {
        timer.cancel();
      }
    });
  }
  
  /// Stop resend countdown timer (public method)
  void stopResendCountdown() {
    _stopCountdown();
  }

  /// Stop resend countdown timer
  void _stopCountdown() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
    _resendCountdown = 0;
  }
  
  /// Validate OTP format and completeness
  bool validateOtp() {
    final otp = otpValue;
    if (otp.length != 6) {
      _setError('Please enter a 6-digit OTP');
      return false;
    }
    
    if (!RegExp(r'^\d{6}$').hasMatch(otp)) {
      _setError('OTP must contain only numbers');
      return false;
    }
    
    return true;
  }
  
  /// Perform OTP verification
  Future<AuthResult> verifyOtp() async {
    if (!validateOtp()) {
      return AuthResult.failure(message: _errorMessage ?? 'Invalid OTP');
    }
    
    try {
      _setLoading(true);
      _setError(null);
      
      AppLogger.userAction('OTP verification attempt');
      
      final result = await _authService.verifyOtp(otpValue);
      
      if (!result.success && result.message != null) {
        _setError(result.message);
        clearOtp(); // Clear OTP on error
      }
      
      return result;
    } catch (e) {
      final message = 'OTP verification failed: ${e.toString()}';
      _setError(message);
      clearOtp();
      AppLogger.error(message);
      return AuthResult.failure(message: message, error: Exception(e.toString()));
    } finally {
      _setLoading(false);
    }
  }
  
  /// Handle resend OTP
  Future<bool> resendOtp() async {
    if (!canResend) {
      return false;
    }
    
    try {
      // For now, just restart the countdown
      // In a real app, you would call the resend OTP API
      startResendCountdown();
      
      AppLogger.userAction('OTP resend requested');
      return true;
    } catch (e) {
      AppLogger.error('OTP resend failed: $e');
      return false;
    }
  }
  
  /// Reset model state
  void reset() {
    clearOtp();
    _isLoading = false;
    _errorMessage = null;
    _stopCountdown();
    notifyListeners();
  }
  
  /// Initialize model (start countdown)
  void initialize() {
    startResendCountdown();
  }
  
  /// Listen to auth service state changes
  void listenToAuthService(VoidCallback onStateChange) {
    _authService.addListener(onStateChange);
  }
  
  /// Remove auth service listener
  void removeAuthServiceListener(VoidCallback onStateChange) {
    _authService.removeListener(onStateChange);
  }
  
  /// Get current auth state
  AuthState get authState => _authService.state;
  
  /// Get auth service error message
  String? get authServiceError => _authService.errorMessage;
  
  @override
  void dispose() {
    _stopCountdown();
    super.dispose();
  }
}
