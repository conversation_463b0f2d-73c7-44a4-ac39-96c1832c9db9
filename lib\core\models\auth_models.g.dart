// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginResponse _$LoginResponseFromJson(Map<String, dynamic> json) =>
    LoginResponse(
      message: json['message'] as String,
      success: json['success'] as bool? ?? true,
    );

Map<String, dynamic> _$LoginResponseToJson(LoginResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'success': instance.success,
    };

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
      token: json['token'] as String,
      activeSessionId: json['active_session_id'] as String,
      user: UserData.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'token': instance.token,
      'active_session_id': instance.activeSessionId,
      'user': instance.user,
    };

UserData _$UserDataFromJson(Map<String, dynamic> json) => UserData(
      id: json['id'] as String,
      username: json['username'] as String,
      role: json['role'] as String,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      centerCode: json['center_code'] as String?,
      centerName: json['center_name'] as String?,
      centerId: json['center_id'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      studentId: json['student_id'] as String?,
      courseId: json['course_id'] as String?,
      courseName: json['course_name'] as String?,
      subjectId: json['subject_id'] as String?,
      subjectName: json['subject_name'] as String?,
      designation: json['designation'] as String?,
    );

Map<String, dynamic> _$UserDataToJson(UserData instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'role': instance.role,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'center_code': instance.centerCode,
      'center_name': instance.centerName,
      'center_id': instance.centerId,
      'phone': instance.phone,
      'address': instance.address,
      'student_id': instance.studentId,
      'course_id': instance.courseId,
      'course_name': instance.courseName,
      'subject_id': instance.subjectId,
      'subject_name': instance.subjectName,
      'designation': instance.designation,
    };

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      username: json['username'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
    };

OtpRequest _$OtpRequestFromJson(Map<String, dynamic> json) => OtpRequest(
      otp: json['otp'] as String,
    );

Map<String, dynamic> _$OtpRequestToJson(OtpRequest instance) =>
    <String, dynamic>{
      'otp': instance.otp,
    };

SessionValidationRequest _$SessionValidationRequestFromJson(
        Map<String, dynamic> json) =>
    SessionValidationRequest(
      userId: json['user_id'] as String,
      activeSessionId: json['active_session_id'] as String,
    );

Map<String, dynamic> _$SessionValidationRequestToJson(
        SessionValidationRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'active_session_id': instance.activeSessionId,
    };

LogoutRequest _$LogoutRequestFromJson(Map<String, dynamic> json) =>
    LogoutRequest(
      userId: json['user_id'] as String,
    );

Map<String, dynamic> _$LogoutRequestToJson(LogoutRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
    };
Faculty _$FacultyFromJson(Map<String, dynamic> json) => Faculty(
      email: json['email'] as String,
      firstName: json['first_name'] as String,
      id: json['id'] as String,
      lastName: json['last_name'] as String,
      phone: json['phone'] as String,
    );

Map<String, dynamic> _$FacultyToJson(Faculty instance) => <String, dynamic>{
      'email': instance.email,
      'first_name': instance.firstName,
      'id': instance.id,
      'last_name': instance.lastName,
      'phone': instance.phone,
    };

Parent _$ParentFromJson(Map<String, dynamic> json) => Parent(
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      parentEmail: json['parent_email'] as String,
      phone: json['phone'] as String,
      relationship: json['relationship'] as String,
    );

Map<String, dynamic> _$ParentToJson(Parent instance) => <String, dynamic>{
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'parent_email': instance.parentEmail,
      'phone': instance.phone,
      'relationship': instance.relationship,
    };

StudentData _$StudentDataFromJson(Map<String, dynamic> json) => StudentData(
      id: json['id'] as String,
      username: json['username'] as String,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      phone: json['phone'] as String?,
      studentEmail: json['student_email'] as String?,
      fullAddress: json['full_address'] as String?,
      aadharNumber: json['aadhar_number'] as String?,
      age: (json['age'] as num?)?.toInt(),
      batchId: json['batch_id'] as String?,
      batchName: json['batch_name'] as String?,
      centerCode: json['center_code'] as String?,
      centerName: json['center_name'] as String?,
      centerPhone: json['center_phone'] as String?,
      course: json['course'] as String?,
      courseId: json['course_id'] as String?,
      createdAt: json['created_at'] as String?,
      dob: json['dob'] as String?,
      gender: json['gender'] as String?,
      grade: json['grade'] as String?,
      isActive: json['is_active'] as bool?,
      marks10th: json['marks_10th'] as String?,
      marks12th: json['marks_12th'] as String?,
      nationality: json['nationality'] as String?,
      religion: json['religion'] as String?,
      studentFaceEmbedding: json['student_face_embedding'] as String?,
    );

Map<String, dynamic> _$StudentDataToJson(StudentData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'phone': instance.phone,
      'student_email': instance.studentEmail,
      'full_address': instance.fullAddress,
      'aadhar_number': instance.aadharNumber,
      'age': instance.age,
      'batch_id': instance.batchId,
      'batch_name': instance.batchName,
      'center_code': instance.centerCode,
      'center_name': instance.centerName,
      'center_phone': instance.centerPhone,
      'course': instance.course,
      'course_id': instance.courseId,
      'created_at': instance.createdAt,
      'dob': instance.dob,
      'gender': instance.gender,
      'grade': instance.grade,
      'is_active': instance.isActive,
      'marks_10th': instance.marks10th,
      'marks_12th': instance.marks12th,
      'nationality': instance.nationality,
      'religion': instance.religion,
      'student_face_embedding': instance.studentFaceEmbedding,
    };

StudentDashboardResponse _$StudentDashboardResponseFromJson(
        Map<String, dynamic> json) =>
    StudentDashboardResponse(
      faculty: (json['faculty'] as List<dynamic>)
          .map((e) => Faculty.fromJson(e as Map<String, dynamic>))
          .toList(),
      kotaTeachers: json['kota_teachers'] as List<dynamic>,
      parent: Parent.fromJson(json['parent'] as Map<String, dynamic>),
      student: StudentData.fromJson(json['student'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$StudentDashboardResponseToJson(
        StudentDashboardResponse instance) =>
    <String, dynamic>{
      'faculty': instance.faculty,
      'kota_teachers': instance.kotaTeachers,
      'parent': instance.parent,
      'student': instance.student,
    };
