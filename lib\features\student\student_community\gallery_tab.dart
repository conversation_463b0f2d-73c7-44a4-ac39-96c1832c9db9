import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../../../core/config/app_config.dart';
import 'student_community_page.dart';

class GalleryTab extends StatefulWidget {
  final CommunityProvider provider;

  const GalleryTab({Key? key, required this.provider}) : super(key: key);

  @override
  _GalleryTabState createState() => _GalleryTabState();
}

class _GalleryTabState extends State<GalleryTab> with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _replyController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  
  File? _uploadFile;
  String _uploadPreview = '';
  bool _isUploadOpen = false;
  bool _isUploading = false;

  late AnimationController _uploadAnimationController;
  late Animation<double> _uploadAnimation;

  @override
  void initState() {
    super.initState();
    _uploadAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _uploadAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _uploadAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _replyController.dispose();
    _descriptionController.dispose();
    _uploadAnimationController.dispose();
    super.dispose();
  }

  Future<void> _uploadImage() async {
    final XFile? file = await _picker.pickImage(source: ImageSource.gallery);
    if (file != null) {
      setState(() {
        _uploadFile = File(file.path);
        _uploadPreview = file.path;
        _isUploadOpen = true;
      });
      _uploadAnimationController.forward();
    }
  }

  void _handleUpload() async {
    if (_uploadFile == null || _isUploading) return;
    setState(() => _isUploading = true);
    try {
      await widget.provider.uploadImage(_uploadFile!, description: _descriptionController.text.trim());
      setState(() {
        _uploadFile = null;
        _uploadPreview = '';
        _descriptionController.clear();
        _isUploadOpen = false;
        _isUploading = false;
      });
      _uploadAnimationController.reset();
    } catch (e) {
      setState(() => _isUploading = false);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Upload failed')));
    }
  }

  void _handleReply(String imageId) async {
    if (_replyController.text.trim().isEmpty || widget.provider.selectedImage == null) return;
    
    final content = _replyController.text.trim();
    try {
      await widget.provider.addThreadToImage(imageId, content);
      _replyController.clear();
      widget.provider.setNewReply(''); // Clear provider state
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not send reply'))
      );
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients && 
        widget.provider.selectedImage?.threads != null && 
        widget.provider.selectedImage!.threads!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  void _showFullImage(String imageUrl) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.contain,
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 360;
    final isMediumScreen = screenWidth < 600;

    return Consumer<CommunityProvider>(
      builder: (context, provider, _) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.grey.shade50,
                Colors.blue.shade200,
                Colors.purple.shade200,
              ],
            ),
          ),
          child: Column(
            children: [
              // Image Grid (Top Section)
              Expanded(
                flex: 2,
                child: Container(
                  margin: EdgeInsets.all(isSmallScreen ? 4 : 8),
                  padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withOpacity(0.1),
                        blurRadius: isSmallScreen ? 10 : 20,
                        offset: Offset(0, isSmallScreen ? 4 : 8),
                      ),
                      BoxShadow(
                        color: Colors.white.withOpacity(0.8),
                        blurRadius: isSmallScreen ? 10 : 20,
                        offset: Offset(0, isSmallScreen ? -4 : -8),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      // Header with Upload Button
                      Container(
                        padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.purple.shade100,
                              Colors.blue.shade100,
                              Colors.indigo.shade50,
                            ],
                          ),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(isSmallScreen ? 16 : 24),
                            topRight: Radius.circular(isSmallScreen ? 16 : 24),
                          ),
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.purple.shade100,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(isSmallScreen ? 6 : 10),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Colors.purple.shade600,
                                        Colors.purple.shade800,
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.purple.withOpacity(0.3),
                                        blurRadius: isSmallScreen ? 4 : 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.photo_library_rounded,
                                    color: Colors.white,
                                    size: isSmallScreen ? 16 : 20,
                                  ),
                                ),
                                SizedBox(width: isSmallScreen ? 8 : 12),
                                Text(
                                  'Gallery',
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 16 : 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.purple.shade700,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.purple.shade500,
                                    Colors.purple.shade700,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.purple.withOpacity(0.3),
                                    blurRadius: isSmallScreen ? 4 : 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: ElevatedButton.icon(
                                onPressed: _uploadImage,
                                icon: Icon(Icons.cloud_upload_rounded, size: isSmallScreen ? 14 : 18),
                                label: Text('Upload', style: TextStyle(fontWeight: FontWeight.w600, fontSize: isSmallScreen ? 12 : 14)),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  foregroundColor: Colors.white,
                                  shadowColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                  ),
                                  padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 12 : 16, vertical: isSmallScreen ? 8 : 12),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Upload Preview Modal
                      if (_isUploadOpen) _buildUploadPreview(),
                      // Image Grid
                      Expanded(
                        child: provider.loadingImages
                            ? Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.purple.shade50,
                                      Colors.blue.shade50,
                                    ],
                                  ),
                                ),
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SpinKitPulse(
                                        color: Colors.purple,
                                        size: isSmallScreen ? 40.0 : 50.0,
                                      ),
                                      SizedBox(height: isSmallScreen ? 12 : 16),
                                      Text(
                                        'Loading gallery...',
                                        style: TextStyle(
                                          color: Colors.purple,
                                          fontSize: isSmallScreen ? 14 : 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            : provider.images.isEmpty
                                ? Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: [
                                                Colors.purple.shade100,
                                                Colors.blue.shade100,
                                              ],
                                            ),
                                            shape: BoxShape.circle,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.purple.withOpacity(0.2),
                                                blurRadius: isSmallScreen ? 10 : 20,
                                                offset: const Offset(0, 8),
                                              ),
                                            ],
                                          ),
                                          child: Icon(
                                            Icons.photo_camera_rounded,
                                            size: isSmallScreen ? 36 : 48,
                                            color: Colors.purple.shade600,
                                          ),
                                        ),
                                        SizedBox(height: isSmallScreen ? 16 : 20),
                                        Text(
                                          'No images yet',
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: isSmallScreen ? 16 : 18,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        SizedBox(height: isSmallScreen ? 6 : 8),
                                        Text(
                                          'Be the first to share!',
                                          style: TextStyle(
                                            color: Colors.grey.shade500,
                                            fontSize: isSmallScreen ? 12 : 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : ListView.builder(
                                    padding: EdgeInsets.all(isSmallScreen ? 8 : 16),
                                    itemCount: provider.images.length,
                                    itemBuilder: (context, index) {
                                      final image = provider.images[index];
                                      final isSelected = provider.selectedImage?.id == image.id;
                                      return GestureDetector(
                    
                                        onTap: () => provider.setSelectedImage(image.id),
                                        child: AnimatedContainer(
                                          duration: const Duration(milliseconds: 400),
                                          curve: Curves.easeInOut,
                                          margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 16),
                                          decoration: BoxDecoration(
                                            gradient: isSelected
                                                ? LinearGradient(
                                                    begin: Alignment.topLeft,
                                                    end: Alignment.bottomRight,
                                                    colors: [
                                                      Colors.purple.shade50,
                                                      Colors.blue.shade50,
                                                      Colors.indigo.shade200,
                                                    ],
                                                  )
                                                : LinearGradient(
                                                    colors: [Colors.white, Colors.white],
                                                  ),
                                            borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                                            border: Border.all(
                                              color: isSelected 
                                                  ? Colors.purple.shade300 
                                                  : Colors.grey.shade200,
                                              width: isSelected ? 2 : 1,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: isSelected 
                                                    ? Colors.purple.withOpacity(0.2)
                                                    : Colors.grey.withOpacity(0.1),
                                                blurRadius: isSelected ? 10 : 15,
                                                offset: Offset(0, isSelected ? 6 : 4),
                                              ),
                                            ],
                                          ),
                                          child: Row(
                                            children: [
                                              Stack(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.only(
                                                      topLeft: Radius.circular(isSmallScreen ? 16 : 20),
                                                      bottomLeft: Radius.circular(isSmallScreen ? 16 : 20),
                                                    ),
                                                    child: CachedNetworkImage(
                                                      imageUrl: image.url,
                                                      width: isSmallScreen ? 70 : 90,
                                                      height: isSmallScreen ? 70 : 90,
                                                      fit: BoxFit.cover,
                                                      placeholder: (context, url) => Container(
                                                        width: isSmallScreen ? 70 : 90,
                                                        height: isSmallScreen ? 70 : 90,
                                                        decoration: BoxDecoration(
                                                          gradient: LinearGradient(
                                                            colors: [
                                                              Colors.grey.shade200,
                                                              Colors.grey.shade300,
                                                            ],
                                                          ),
                                                        ),
                                                        child: Center(
                                                          child: SpinKitFadingCircle(
                                                            color: Colors.grey,
                                                            size: isSmallScreen ? 16 : 20,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  if (isSelected)
                                                    Positioned(
                                                      top: isSmallScreen ? 6 : 8,
                                                      left: isSmallScreen ? 6 : 8,
                                                      child: Container(
                                                        padding: EdgeInsets.all(isSmallScreen ? 2 : 4),
                                                        decoration: BoxDecoration(
                                                          color: Colors.purple.shade600,
                                                          borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8),
                                                        ),
                                                        child: Icon(
                                                          Icons.check_rounded,
                                                          color: Colors.white,
                                                          size: isSmallScreen ? 12 : 16,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                              Expanded(
                                                child: Padding(
                                                  padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        image.description ?? 'Untitled Image',
                                                        style: TextStyle(
                                                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                                          color: isSelected ? Colors.purple.shade800 : Colors.grey.shade800,
                                                          fontSize: isSmallScreen ? 14 : 15,
                                                          height: 1.3,
                                                        ),
                                                        maxLines: 2,
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                      SizedBox(height: isSmallScreen ? 6 : 8),
                                                      Row(
                                                        children: [
                                                          Container(
                                                            width: isSmallScreen ? 6 : 10,
                                                            height: isSmallScreen ? 6 : 10,
                                                            decoration: BoxDecoration(
                                                              gradient: LinearGradient(
                                                                colors: [Colors.green.shade400, Colors.green.shade600],
                                                              ),
                                                              shape: BoxShape.circle,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  color: Colors.green.withOpacity(0.3),
                                                                  blurRadius: isSmallScreen ? 2 : 4,
                                                                  offset: const Offset(0, 2),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          SizedBox(width: isSmallScreen ? 6 : 10),
                                                          Expanded(
                                                            child: Text(
                                                              '${image.firstName ?? 'You'} • ${image.senderType ?? ''}',
                                                              style: TextStyle(
                                                                fontSize: isSmallScreen ? 12 : 13,
                                                                color: Colors.grey.shade600,
                                                                fontWeight: FontWeight.w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      if (image.threads != null && image.threads!.isNotEmpty)
                                                        Container(
                                                          margin: EdgeInsets.only(top: isSmallScreen ? 6 : 8),
                                                          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 6 : 10, vertical: isSmallScreen ? 4 : 6),
                                                          decoration: BoxDecoration(
                                                            gradient: LinearGradient(
                                                              colors: [
                                                                Colors.purple.shade100,
                                                                Colors.blue.shade100,
                                                              ],
                                                            ),
                                                            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                                                            border: Border.all(
                                                              color: Colors.purple.shade200,
                                                              width: 1,
                                                            ),
                                                          ),
                                                          child: Row(
                                                            mainAxisSize: MainAxisSize.min,
                                                            children: [
                                                              Icon(
                                                                Icons.chat_bubble_outline_rounded,
                                                                size: isSmallScreen ? 10 : 12,
                                                                color: Colors.purple.shade600,
                                                              ),
                                                              SizedBox(width: isSmallScreen ? 2 : 4),
                                                              Text(
                                                                '${image.threads!.length} comments',
                                                                style: TextStyle(
                                                                  fontSize: isSmallScreen ? 10 : 11,
                                                                  color: Colors.purple.shade700,
                                                                  fontWeight: FontWeight.w600,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                      ),
                    ],
                  ),
                ),
              ),
              // Image Conversation (Bottom Section)
              Expanded(
                flex: 3,
                child: provider.selectedImage == null
                    ? Container(
                        margin: EdgeInsets.all(isSmallScreen ? 4 : 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.grey.shade50,
                              Colors.grey.shade100,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                          border: Border.all(
                            color: Colors.grey.shade200,
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TweenAnimationBuilder<double>(
                                tween: Tween(begin: 0.0, end: 1.0),
                                duration: const Duration(milliseconds: 800),
                                builder: (context, value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: Container(
                                      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.purple.shade100,
                                            Colors.blue.shade100,
                                          ],
                                        ),
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.purple.withOpacity(0.2),
                                            blurRadius: isSmallScreen ? 10 : 20,
                                            offset: const Offset(0, 8),
                                          ),
                                        ],
                                      ),
                                      child: Icon(
                                        Icons.image_outlined,
                                        size: isSmallScreen ? 36 : 48,
                                        color: Colors.purple.shade600,
                                      ),
                                    ),
                                  );
                                },
                              ),
                              SizedBox(height: isSmallScreen ? 16 : 24),
                              Text(
                                'Select an image to start discussion',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 16 : 18,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: isSmallScreen ? 6 : 8),
                              Text(
                                'Choose from the gallery above',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 12 : 14,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : _buildImageConversation(provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUploadPreview() {
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    return AnimatedBuilder(
      animation: _uploadAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _uploadAnimation.value,
          child: Container(
            margin: EdgeInsets.all(isSmallScreen ? 8 : 16),
            padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white,
                  Colors.purple.shade50,
                ],
              ),
              borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
              border: Border.all(
                color: Colors.purple.shade200,
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withOpacity(0.2),
                  blurRadius: isSmallScreen ? 10 : 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '📸 Upload Preview',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 16 : 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple.shade700,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() => _isUploadOpen = false);
                        _uploadAnimationController.reset();
                      },
                      icon: Icon(
                        Icons.close_rounded,
                        color: Colors.grey.shade600,
                        size: isSmallScreen ? 16 : 20,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                if (_uploadFile != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    child: Image.file(
                      _uploadFile!,
                      height: isSmallScreen ? 150 : 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                TextField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    hintText: '✨ Add a description...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                      borderSide: BorderSide(color: Colors.purple.shade400),
                    ),
                  ),
                  maxLines: 3,
                ),
                SizedBox(height: isSmallScreen ? 12 : 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() => _isUploadOpen = false);
                          _uploadAnimationController.reset();
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.grey.shade400),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                          ),
                        ),
                        child: Text('Cancel', style: TextStyle(fontSize: isSmallScreen ? 12 : 14)),
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 12),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.purple.shade500,
                              Colors.purple.shade700,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                        ),
                        child: ElevatedButton(
                          onPressed: _isUploading ? null : _handleUpload,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                            ),
                          ),
                          child: _isUploading
                              ? SizedBox(
                                  width: isSmallScreen ? 16 : 20,
                                  height: isSmallScreen ? 16 : 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: isSmallScreen ? 1.5 : 2,
                                  ),
                                )
                              : Text(
                                  'Upload',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: isSmallScreen ? 12 : 14,
                                  ),
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildImageConversation(CommunityProvider provider) {
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 4 : 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            blurRadius: isSmallScreen ? 10 : 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Image Header
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.purple.shade50,
                  Colors.blue.shade50,
                  Colors.indigo.shade200,
                ],
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(isSmallScreen ? 16 : 24),
                topRight: Radius.circular(isSmallScreen ? 16 : 24),
              ),
              border: Border(
                bottom: BorderSide(color: Colors.purple.shade100),
              ),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => _showFullImage(provider.selectedImage!.url),
                  child: Hero(
                    tag: provider.selectedImage!.id,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.purple.withOpacity(0.3),
                            blurRadius: isSmallScreen ? 4 : 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                        child: CachedNetworkImage(
                          imageUrl: provider.selectedImage!.url,
                          width: isSmallScreen ? 50 : 70,
                          height: isSmallScreen ? 50 : 70,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider.selectedImage!.description ?? 'Untitled Image',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 16 : 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple.shade800,
                          height: 1.3,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 6),
                      Row(
                        children: [
                          Container(
                            width: isSmallScreen ? 6 : 8,
                            height: isSmallScreen ? 6 : 8,
                            decoration: BoxDecoration(
                              color: Colors.green.shade500,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: isSmallScreen ? 6 : 8),
                          Text(
                            '${provider.selectedImage!.firstName ?? 'Anonymous'} • ${provider.selectedImage!.senderType ?? ''} • ${provider.formatRelativeTime(provider.selectedImage!.createdAt!)}',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 11 : 13,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  ),
                  child: IconButton(
                    onPressed: () => provider.setSelectedImage(null),
                    icon: Icon(
                      Icons.close_rounded,
                      color: Colors.grey.shade600,
                      size: isSmallScreen ? 16 : 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Comments/Threads
          Expanded(
            child: provider.loadingFullImage
                ? Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.purple.shade50,
                          Colors.blue.shade50,
                        ],
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpinKitPulse(color: Colors.purple, size: isSmallScreen ? 40 : 50),
                          SizedBox(height: isSmallScreen ? 12 : 16),
                          Text(
                            'Loading comments...',
                            style: TextStyle(
                              color: Colors.purple,
                              fontSize: isSmallScreen ? 14 : 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : provider.selectedImage!.threads == null || provider.selectedImage!.threads!.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.purple.shade50,
                                    Colors.blue.shade50,
                                  ],
                                ),
                                shape: BoxShape.circle,
                              ),
                              child: Text(
                                '💬',
                                style: TextStyle(fontSize: isSmallScreen ? 24 : 32),
                              ),
                            ),
                            SizedBox(height: isSmallScreen ? 12 : 16),
                            Text(
                              'Be the first to comment on this image!',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: isSmallScreen ? 16 : 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: isSmallScreen ? 6 : 8),
                            Text(
                              'Share your thoughts below',
                              style: TextStyle(
                                color: Colors.grey.shade500,
                                fontSize: isSmallScreen ? 12 : 14,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        controller: _scrollController,
                        padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                        itemCount: provider.selectedImage!.threads!.length,
                        itemBuilder: (context, index) {
                          final thread = provider.selectedImage!.threads![index];
                          final isMyComment = thread.senderId == provider.user!.id;
                          
                          return Container(
                            margin: EdgeInsets.only(
                              left: isMyComment ? 30 : 8,
                              right: isMyComment ? 8 : 30,
                              bottom: isSmallScreen ? 8 : 16,
                            ),
                            child: Row(
                              mainAxisAlignment: isMyComment 
                                  ? MainAxisAlignment.end 
                                  : MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (!isMyComment) ...[
                                  Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.purple.shade300,
                                          Colors.purple.shade500,
                                        ],
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.purple.withOpacity(0.3),
                                          blurRadius: isSmallScreen ? 4 : 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: CircleAvatar(
                                      radius: isSmallScreen ? 14 : 18,
                                      backgroundColor: Colors.transparent,
                                      child: Text(
                                        thread.firstName![0].toUpperCase(),
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 12 : 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: isSmallScreen ? 8 : 12),
                                ],
                                Flexible(
                                  child: Container(
                                    padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                                    decoration: BoxDecoration(
                                      gradient: isMyComment 
                                          ? LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: [
                                                Colors.purple.shade500,
                                                Colors.purple.shade700,
                                              ],
                                            )
                                          : LinearGradient(
                                              colors: [
                                                Colors.grey.shade50,
                                                Colors.grey.shade100,
                                              ],
                                            ),
                                      borderRadius: BorderRadius.only(
                                        topLeft: const Radius.circular(16),
                                        topRight: const Radius.circular(16),
                                        bottomLeft: Radius.circular(isMyComment ? 16 : 4),
                                        bottomRight: Radius.circular(isMyComment ? 4 : 16),
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: isMyComment 
                                              ? Colors.purple.withOpacity(0.3)
                                              : Colors.grey.withOpacity(0.2),
                                          blurRadius: isSmallScreen ? 4 : 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        if (!isMyComment)
                                          Padding(
                                            padding: EdgeInsets.only(bottom: isSmallScreen ? 4 : 6),
                                            child: Text(
                                              '${thread.firstName} • ${thread.senderType}',
                                              style: TextStyle(
                                                fontSize: isSmallScreen ? 10 : 11,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ),
                                        Text(
                                          thread.content,
                                          style: TextStyle(
                                            color: isMyComment 
                                                ? Colors.white 
                                                : Colors.grey.shade800,
                                            fontSize: isSmallScreen ? 14 : 15,
                                            height: 1.4,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        SizedBox(height: isSmallScreen ? 4 : 6),
                                        Text(
                                          provider.formatRelativeTime(thread.createdAt),
                                          style: TextStyle(
                                            fontSize: isSmallScreen ? 10 : 11,
                                            color: isMyComment 
                                                ? Colors.white.withOpacity(0.8)
                                                : Colors.grey.shade500,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                if (isMyComment) ...[
                                  SizedBox(width: isSmallScreen ? 8 : 12),
                                  Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.purple.shade600,
                                          Colors.purple.shade800,
                                        ],
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.purple.withOpacity(0.4),
                                          blurRadius: isSmallScreen ? 4 : 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: CircleAvatar(
                                      radius: isSmallScreen ? 14 : 18,
                                      backgroundColor: Colors.transparent,
                                      child: Text(
                                        thread.firstName![0].toUpperCase(),
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 12 : 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          );
                        },
                      ),
          ),
          // Comment Input
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(color: Colors.grey.shade200),
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(isSmallScreen ? 16 : 24),
                bottomRight: Radius.circular(isSmallScreen ? 16 : 24),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      controller: _replyController,
                      onChanged: provider.setNewReply,
                      decoration: InputDecoration(
                        hintText: '💭 Comment on this image...',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                          borderSide: BorderSide(
                            color: Colors.purple.shade400,
                            width: 2,
                          ),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 12 : 20,
                          vertical: isSmallScreen ? 10 : 14,
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      maxLines: null,
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: provider.newReply.isNotEmpty
                          ? [Colors.purple.shade500, Colors.purple.shade700]
                          : [Colors.grey.shade300, Colors.grey.shade400],
                    ),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                    boxShadow: provider.newReply.isNotEmpty
                        ? [
                            BoxShadow(
                              color: Colors.purple.withOpacity(0.4),
                              blurRadius: isSmallScreen ? 4 : 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : [],
                  ),
                  child: IconButton(
                    onPressed: provider.newReply.isNotEmpty 
                        ? () => _handleReply(provider.selectedImage!.id) 
                        : null,
                    icon: const Icon(Icons.send_rounded),
                    color: Colors.white,
                    iconSize: isSmallScreen ? 16 : 20,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}