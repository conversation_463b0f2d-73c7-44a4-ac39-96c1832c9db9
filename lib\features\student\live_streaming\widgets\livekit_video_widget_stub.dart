import 'package:flutter/material.dart';

// Stub implementation for web platform
class LiveKitVideoWidget extends StatelessWidget {
  final String livekitUrl;
  final String livekitToken;
  final String participantName;
  final bool isTeacher;

  const LiveKitVideoWidget({
    super.key,
    required this.livekitUrl,
    required this.livekitToken,
    required this.participantName,
    required this.isTeacher,
  });

  

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.web,
              size: 64,
              color: Colors.white54,
            ),
            SizedBox(height: 16),
            Text(
              'LiveKit Web Support Limited',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Please use mobile app for video streaming',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
