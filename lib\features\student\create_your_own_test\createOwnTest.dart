import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../../core/config/app_config.dart';
import '../onboarding_assessment/service/onboardingass.service.dart';
import 'exam_and_subject_selection.dart';
import 'unit_and_subtopic_selection.dart';
import 'test_display_wrapper.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/models/auth_models.dart';
import '../face_verification/face_verification_model.dart';
import '../face_verification/face_verification_controller.dart';
import '../face_verification/face_verification_view.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math';

class CreateYourOwnTestParent extends StatefulWidget {
  const CreateYourOwnTestParent({super.key});

  @override
  State<CreateYourOwnTestParent> createState() =>
      _CreateYourOwnTestParentState();
}

class _CreateYourOwnTestParentState extends State<CreateYourOwnTestParent> {
  String _selectedExam = '';
  String _selectedModule = '';
  List<String> _selectedUnits = [];
  List<String> _selectedSubtopics = [];
  bool _testStarted = false;
  bool? _isAssessmentCompleted;
  String? _userId;
  String? _courseName;
  bool _isFaceVerified = false;
  bool _isVerifyingFace = false;
  int _faceVerificationAttempts = 0;
  final int _maxVerificationAttempts = 3;
  

  @override
  void initState() {
    super.initState();
    _loadUserDataAndCheckStatus();
    _initiateFaceVerification();
  }

  @override
  void didUpdateWidget(CreateYourOwnTestParent oldWidget) {
    super.didUpdateWidget(oldWidget);
    _loadUserDataAndCheckStatus();
    if (!_isFaceVerified) {
      _initiateFaceVerification();
    }
  }

  Future<void> _loadUserDataAndCheckStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? dashboardDataString =
          prefs.getString(AppConfig.studentDashboardKey);
      StudentDashboardResponse? dashboardData;

      if (dashboardDataString != null) {
        dashboardData =
            StudentDashboardResponse.fromJson(jsonDecode(dashboardDataString));
      } else {
        dashboardData = await ApiService().getStudentDashboard();
        await ApiService().saveStudentDashboard(dashboardData);
        dashboardDataString = prefs.getString(AppConfig.studentDashboardKey);
        if (AppConfig.isDebug) {
          print('Fetched and saved dashboard data: $dashboardDataString');
        }
      }

      if (dashboardData != null) {
        setState(() {
          _userId = dashboardData?.student.id;
          _courseName = dashboardData?.student.course;
        });
      } else {
        _userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
        await prefs.setString(
          AppConfig.userDataKey,
          jsonEncode({'id': _userId}),
        );
      }

      if (AppConfig.isDebug) {
        print('User ID: $_userId');
        print('Course Name: ${_courseName ?? 'Not available'}');
      }

      final dio = Dio(BaseOptions(
        baseUrl: AppConfig.apiBaseUrl,
        headers: AppConfig.defaultHeaders,
        connectTimeout: AppConfig.connectionTimeout,
        receiveTimeout: AppConfig.apiTimeout,
      ));
      dio.interceptors.add(LogInterceptor(
        request: true,
        requestBody: true,
        responseBody: true,
        error: true,
      ));

      final service = AssessmentService(dio);
      try {
        final isAssessmentCompleted = await service.checkAssessmentStatus();
        setState(() {
          _isAssessmentCompleted = isAssessmentCompleted;
        });
        if (AppConfig.isDebug) {
          print(
              'Assessment Status: ${isAssessmentCompleted ? 'Completed' : 'Not Completed'}');
        }
      } catch (assessmentError) {
        if (AppConfig.isDebug) {
          print('Error checking assessment status: $assessmentError');
          print('Defaulting to assessment completed = true for testing');
        }
        // Fallback: If assessment status check fails, assume it's completed
        // This prevents the white screen issue when the API call fails
        setState(() {
          _isAssessmentCompleted = true;
        });
      }
    } catch (e) {
      if (AppConfig.isDebug) {
        print('Error in _loadUserDataAndCheckStatus: $e');
      }
      setState(() {
        // Fallback: If everything fails, assume assessment is completed
        // This prevents the white screen issue
        _isAssessmentCompleted = true;
      });
    }
  }

  void _handleRetry() {
    _initiateFaceVerification();
  }

  Future<void> _initiateFaceVerification() async {
    if (_userId == null) {
      if (AppConfig.isDebug) {
        print('Error: User ID is null');
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'User ID not available. Please try again.',
            // style: GoogleFonts.balsamiqSans(fontSize: 16, color: Colors.white),
          ),
          backgroundColor: Colors.redAccent,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          margin: EdgeInsets.all(16),
          duration: Duration(seconds: 3),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.yellow,
            onPressed: _initiateFaceVerification,
          ),
        ),
      );
      return;
    }

    setState(() {
      _isVerifyingFace = true;
      _faceVerificationAttempts++;
      if (AppConfig.isDebug) {
        print(
            'Starting face verification attempt #$_faceVerificationAttempts for userId: $_userId');
      }
    });

    final controller =
        FaceVerificationController(FaceVerificationModel(ApiService()));
    try {
      final verified = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return FaceVerificationView(
            userId: _userId!,
            onVerificationComplete: (success) {
              if (AppConfig.isDebug) {
                print('Face verification result: $success');
                if (!success) {
                  print(
                      'Verification failed. Controller error: ${controller.errorMessage}');
                  print('Controller response: ${controller.res}');
                }
              }
              Navigator.pop(context, success);
            },
            controller: controller,
          );
        },
      );

      setState(() {
        _isFaceVerified = verified ?? false;
        _isVerifyingFace = false;
      });

      if (AppConfig.isDebug) {
        print('Face verification completed. Status: $_isFaceVerified');
        print('Assessment status: $_isAssessmentCompleted');
        print('Should show content: ${_isAssessmentCompleted == true && _isFaceVerified}');
      }

      if (!_isFaceVerified) {
        if (AppConfig.isDebug) {
          print(
              'Face verification failed for attempt #$_faceVerificationAttempts');
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Face verification failed. ${_faceVerificationAttempts < _maxVerificationAttempts ? 'Please try again.' : 'Too many attempts.'}',
              // style: GoogleFonts.balsamiqSans(fontSize: 16, color: Colors.white),
            ),
            backgroundColor: Colors.redAccent,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            margin: EdgeInsets.all(16),
            duration: Duration(seconds: 3),
           
          ),
        );

        if (_faceVerificationAttempts >= _maxVerificationAttempts) {
          if (AppConfig.isDebug) {
            print(
                'Max verification attempts reached. Logging out user: $_userId');
          }
          ApiService().logout(_userId!).then((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Too many failed attempts. Logging out.',
                  // style: GoogleFonts.balsamiqSans(fontSize: 16, color: Colors.white),
                ),
                backgroundColor: Colors.redAccent,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                margin: EdgeInsets.all(16),
              ),
            );
            context.go('/login');
          });
        }
      } else {
        _faceVerificationAttempts = 0;
        if (AppConfig.isDebug) {
          print('Face verification successful');
        }
      }
    } catch (e) {
      if (AppConfig.isDebug) {
        print('Error during face verification: $e');
      }
      setState(() {
        _isVerifyingFace = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Face verification error: $e',
            // style: GoogleFonts.balsamiqSans(fontSize: 16, color: Colors.white),
          ),
          backgroundColor: Colors.redAccent,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          margin: EdgeInsets.all(16),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: !_isFaceVerified
          ? (_isVerifyingFace
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 8),
                        Text(
                          'Verifying your face...',
                          style: TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                )
              : Stack(
                  children: [
                    // Animated Background
                    Animate(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.blue[100]!, Colors.purple[100]!],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                      ),
                      effects: [
                        FadeEffect(duration: Duration(milliseconds: 1000)),
                        CustomEffect(
                          delay: Duration(milliseconds: 500),
                          duration: Duration(seconds: 3),
                          builder: (context,value, child) {
                            return Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.blue[100]!,
                                    Colors.purple[100]!
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  transform: GradientRotation(
                                      value * 2 * pi), // Rotate gradient
                                ),
                              ),
                              child: child,
                            );
                          },
                        ),
                      ],
                    ),
                    // Centered content with SVG icon
                    Container(
                      color: Colors.transparent,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Animated Face scan icon (SVG)
                            Animate(
                              child: SvgPicture.asset(
                                'assets/icons/face_scan.svg',
                                width: 200,
                                height: 200,
                                
                              ),
                              effects: [
                                FadeEffect(
                                    duration: Duration(milliseconds: 500)),
                                ScaleEffect(delay: Duration(milliseconds: 200)),
                              ],
                            ),
                            const SizedBox(height: 20),
                            // Animated Main text
                            Animate(
                              child: Text(
                                'Scan your face to unlock the test',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue[900],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              effects: [
                                FadeEffect(
                                    duration: Duration(milliseconds: 600)),
                                SlideEffect(
                                  begin: const Offset(0, 0.5),
                                  end: Offset.zero,
                                  duration: Duration(milliseconds: 500),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            // Animated Subtext
                            Animate(
                              child: Text(
                                'The test will unlock if you are a verified user.',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              effects: [
                                FadeEffect(
                                    duration: Duration(milliseconds: 700)),
                                SlideEffect(
                                  begin: const Offset(0, 0.5),
                                  end: Offset.zero,
                                  duration: Duration(milliseconds: 500),
                                ),
                              ],
                            ),
                            const SizedBox(height: 30),
                            // Animated Get Started button
                            Animate(
                              child: ElevatedButton(
                                onPressed: _userId == null
                                    ? null
                                    : () => _initiateFaceVerification(),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue[700],
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 30, vertical: 15),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: const Text(
                                  'Get Started',
                                  style: TextStyle(
                                      fontSize: 16, color: Colors.white),
                                ),
                              ),
                              effects: [
                                FadeEffect(
                                    duration: Duration(milliseconds: 800)),
                                ScaleEffect(
                                  delay: Duration(milliseconds: 200),
                                  duration: Duration(milliseconds: 500),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            // Animated Report a problem
                          ],
                        ),
                      ),
                    ),
                  ],
                ))
          : AnimatedSwitcher(
              duration: const Duration(milliseconds: 600),
              child: _isAssessmentCompleted == true && _isFaceVerified
                  ? _buildContent()
                  : _buildWaitingOrErrorState(),
            ),
    );
  }

  Widget _buildWaitingOrErrorState() {
    if (_isFaceVerified && _isAssessmentCompleted == null) {
      // Face verified but assessment status is loading/unknown
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text(
                'Loading assessment status...',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => _loadUserDataAndCheckStatus(),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    } else if (_isFaceVerified && _isAssessmentCompleted == false) {
      // Face verified but assessment not completed
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.assignment_outlined,
                size: 64,
                color: Colors.orange[600],
              ),
              const SizedBox(height: 16),
              const Text(
                'Assessment Required',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Please complete your onboarding assessment before accessing the test.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => _loadUserDataAndCheckStatus(),
                child: const Text('Check Again'),
              ),
            ],
          ),
        ),
      );
    } else {
      // Default case - should not normally reach here after face verification
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Loading...',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              if (AppConfig.isDebug) ...[
                Text(
                  'Debug Info:\nFace Verified: $_isFaceVerified\nAssessment Completed: $_isAssessmentCompleted',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _loadUserDataAndCheckStatus(),
                  child: const Text('Retry Loading'),
                ),
              ],
            ],
          ),
        ),
      );
    }
  }

  Widget _buildContent() {
    if (_selectedModule.isEmpty) {
      return ExamAndSubjectSelection(
        selectedExam: _selectedExam,
        selectedModule: _selectedModule,
        setSelectedExam: (value) => setState(() => _selectedExam = value),
        setSelectedModule: (value) => setState(() => _selectedModule = value),
        courseName: _courseName,
      );
    } else if (!_testStarted) {
      return UnitAndSubtopicSelection(
        selectedExam: _selectedExam,
        selectedModule: _selectedModule,
        selectedUnits: _selectedUnits,
        selectedSubtopics: _selectedSubtopics,
        setSelectedUnits: (value) => setState(() => _selectedUnits = value),
        setSelectedSubtopics: (value) =>
            setState(() => _selectedSubtopics = value),
        setTestStarted: (value) => setState(() => _testStarted = value),
      );
    } else {
      return const TestDisplayWrapper();
    }
  }
}
