// // import 'package:flutter/material.dart';
// // import 'package:provider/provider.dart';
// // import 'package:intl/intl.dart';
// // import 'dart:ui';
// // import 'package:go_router/go_router.dart';
// // import '../../core/services/token_service.dart';
// // import '../../core/services/api_service.dart';
// // import '../../core/utils/logger.dart';
// // import '../../core/theme/app_theme.dart';
// // import '../../core/widgets/base_page.dart';

// // // Parent Data Model
// // class Parent {
// //   final String username;
// //   final String firstName;
// //   final String lastName;
// //   final String email;
// //   final String phone;
// //   final String occupation;
// //   final String relationship;
// //   final String createdAt;
// //   final double? annualIncome;
// //   final String studentId;
// //   final bool isActive;
// //   final Map<String, dynamic>? student;

// //   Parent({
// //     required this.username,
// //     required this.firstName,
// //     required this.lastName,
// //     required this.email,
// //     required this.phone,
// //     required this.occupation,
// //     required this.relationship,
// //     required this.createdAt,
// //     this.annualIncome,
// //     required this.studentId,
// //     required this.isActive,
// //     this.student,
// //   });

// //   factory Parent.fromJson(Map<String, dynamic> json) {
// //     return Parent(
// //       username: json['username'] ?? 'Unknown',
// //       firstName: json['first_name'] ?? 'Unknown',
// //       lastName: json['last_name'] ?? 'Unknown',
// //       email: json['parent_email'] ?? 'Unknown',
// //       phone: json['phone'] ?? 'Unknown',
// //       occupation: json['occupation'] ?? 'Unknown',
// //       relationship: json['relationship'] ?? 'Unknown',
// //       createdAt: json['created_at'] ?? 'Unknown',
// //       annualIncome: json['annual_income_inr']?.toDouble(),
// //       studentId: json['student_id'] ?? 'Unknown',
// //       isActive: json['is_active'] ?? false,
// //       student: json['student'],
// //     );
// //   }
// // }

// // // Parent Provider for state management
// // class ParentProvider with ChangeNotifier {
// //   Parent? _parent;
// //   bool _isLoading = true;
// //   bool _isError = false;
// //   String _errorMessage = '';

// //   Parent? get parent => _parent;
// //   bool get isLoading => _isLoading;
// //   bool get isError => _isError;
// //   String get errorMessage => _errorMessage;

// //   final TokenService _tokenService = TokenService();
// //   final ApiService _apiService = ApiService();

// //   Future<void> fetchParentData() async {
// //     _setLoading(true);
// //     _setError(false, '');

// //     try {
// //       AppLogger.info('Fetching parent dashboard data');
// //       final currentToken = await _tokenService.getToken();

// //       if (currentToken == null || !(await _tokenService.isTokenValid())) {
// //         AppLogger.warning('Token validation failed');
// //         _setError(true, 'Authentication token has expired. Please log in again.');
// //         return;
// //       }

// //       final data = await _apiService.fetchParentDashboard();
// //       final parentData = Parent.fromJson(data['parent'] ?? data);

// //       _parent = parentData;
// //       _setLoading(false);
// //       AppLogger.info('Parent dashboard data loaded successfully');
// //     } catch (e) {
// //       AppLogger.error('Failed to fetch parent data: $e');
// //       String msg = 'Unable to fetch parent details. Please try again.';
// //       if (e.toString().contains('401')) {
// //         msg = 'Session expired. Please log in again.';
// //       } else if (e.toString().contains('Network')) {
// //         msg = 'Network error. Please check your connection.';
// //       }
// //       _setError(true, msg);
// //       _setLoading(false);
// //     }
// //   }

// //   Future<void> refreshData() async {
// //     AppLogger.userAction('Parent dashboard refresh', {'timestamp': DateTime.now().toIso8601String()});
// //     await fetchParentData();
// //   }

// //   void _setLoading(bool loading) {
// //     _isLoading = loading;
// //     notifyListeners();
// //   }

// //   void _setError(bool error, String message) {
// //     _isError = error;
// //     _errorMessage = message;
// //     notifyListeners();
// //   }
// // }

// // // Parent Dashboard Page - Main widget for parent role dashboard
// // class ParentDashboardPage extends StatefulWidget {
// //   const ParentDashboardPage({super.key});

// //   @override
// //   State<ParentDashboardPage> createState() => _ParentDashboardPageState();
// // }

// // class _ParentDashboardPageState extends State<ParentDashboardPage>
// //     with SingleTickerProviderStateMixin {
// //   late TabController _tabController;
// //   late ParentProvider _parentProvider;

// //   @override
// //   void initState() {
// //     super.initState();
// //     AppLogger.navigation('Parent Dashboard', 'Page initialized');
// //     _tabController = TabController(length: 3, vsync: this);
// //     _parentProvider = ParentProvider();
// //     _parentProvider.fetchParentData();
// //   }

// //   @override
// //   void dispose() {
// //     _tabController.dispose();
// //     _parentProvider.dispose();
// //     super.dispose();
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     return ChangeNotifierProvider.value(
// //       value: _parentProvider,
// //       child: BasePage(
// //         title: 'Parent Dashboard',
// //         subtitle: 'Manage your child\'s education journey',
// //         breadcrumbs: const ['Dashboard', 'Parent'],
// //         actions: [
// //           IconButton(
// //             onPressed: () {
// //               AppLogger.userAction('Parent dashboard refresh', {'source': 'app_bar'});
// //               _parentProvider.refreshData();
// //             },
// //             icon: const Icon(Icons.refresh),
// //             tooltip: 'Refresh',
// //           ),
// //         ],
// //         child: Consumer<ParentProvider>(
// //           builder: (context, provider, child) {
// //             if (provider.isLoading) {
// //               return _buildLoadingView();
// //             }

// //             if (provider.isError) {
// //               return _buildErrorView(provider);
// //             }

// //             return _buildDashboardContent(provider.parent);
// //           },
// //         ),
// //       ),
// //     );
// //   }

// //   Widget _buildLoadingView() {
// //     return const Center(
// //       child: Column(
// //         mainAxisAlignment: MainAxisAlignment.center,
// //         children: [
// //           CircularProgressIndicator(),
// //           SizedBox(height: 16),
// //           Text('Loading parent dashboard...'),
// //         ],
// //       ),
// //     );
// //   }

// //   Widget _buildErrorView(ParentProvider provider) {
// //     return Center(
// //       child: Column(
// //         mainAxisAlignment: MainAxisAlignment.center,
// //         children: [
// //           const Icon(Icons.error_outline, size: 64, color: Colors.red),
// //           const SizedBox(height: 16),
// //           const Text(
// //             'Error Loading Dashboard',
// //             style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
// //           ),
// //           const SizedBox(height: 8),
// //           Text(
// //             provider.errorMessage,
// //             textAlign: TextAlign.center,
// //             style: const TextStyle(color: Colors.grey),
// //           ),
// //           const SizedBox(height: 16),
// //           ElevatedButton.icon(
// //             onPressed: () {
// //               AppLogger.userAction('Parent dashboard retry', {'error': provider.errorMessage});
// //               provider.refreshData();
// //             },
// //             icon: const Icon(Icons.refresh),
// //             label: const Text('Retry'),
// //           ),
// //         ],
// //       ),
// //     );
// //   }

// //   Widget _buildDashboardContent(Parent? parent) {
// //     if (parent == null) {
// //       return const Center(child: Text('No parent data available'));
// //     }

// //     return Column(
// //       children: [
// //         // Tab Bar
// //         TabBar(
// //           controller: _tabController,
// //           tabs: const [
// //             Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
// //             Tab(icon: Icon(Icons.school), text: 'Student'),
// //             Tab(icon: Icon(Icons.attach_money), text: 'Financial'),
// //           ],
// //         ),
// //         // Tab Content
// //         Expanded(
// //           child: TabBarView(
// //             controller: _tabController,
// //             children: [
// //               _buildOverviewTab(parent),
// //               _buildStudentTab(parent),
// //               _buildFinancialTab(parent),
// //             ],
// //           ),
// //         ),
// //       ],
// //     );
// //   }

// //   Widget _buildOverviewTab(Parent parent) {
// //     return SingleChildScrollView(
// //       padding: const EdgeInsets.all(16),
// //       child: Column(
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           // Welcome Card
// //           Card(
// //             child: Padding(
// //               padding: const EdgeInsets.all(16),
// //               child: Column(
// //                 crossAxisAlignment: CrossAxisAlignment.start,
// //                 children: [
// //                   Text(
// //                     'Welcome, ${parent.firstName} ${parent.lastName}!',
// //                     style: AppTheme.headingMedium,
// //                   ),
// //                   const SizedBox(height: 8),
// //                   Text(
// //                     'Here\'s an overview of your account and your child\'s progress.',
// //                     style: AppTheme.bodyMedium,
// //                   ),
// //                 ],
// //               ),
// //             ),
// //           ),
// //           const SizedBox(height: 16),
          
// //           // Quick Stats
// //           GridView.count(
// //             shrinkWrap: true,
// //             physics: const NeverScrollableScrollPhysics(),
// //             crossAxisCount: 2,
// //             crossAxisSpacing: 16,
// //             mainAxisSpacing: 16,
// //             childAspectRatio: 1.5,
// //             children: [
// //               _buildStatCard(
// //                 icon: Icons.person,
// //                 title: 'Parent',
// //                 value: '${parent.firstName} ${parent.lastName}',
// //                 color: Colors.blue,
// //               ),
// //               _buildStatCard(
// //                 icon: Icons.phone,
// //                 title: 'Contact',
// //                 value: parent.phone,
// //                 color: Colors.green,
// //               ),
// //               _buildStatCard(
// //                 icon: Icons.email,
// //                 title: 'Email',
// //                 value: parent.email,
// //                 color: Colors.orange,
// //               ),
// //               _buildStatCard(
// //                 icon: Icons.work,
// //                 title: 'Occupation',
// //                 value: parent.occupation,
// //                 color: Colors.purple,
// //               ),
// //             ],
// //           ),
// //         ],
// //       ),
// //     );
// //   }

// //   Widget _buildStudentTab(Parent parent) {
// //     final studentData = parent.student;

// //     return SingleChildScrollView(
// //       padding: const EdgeInsets.all(16),
// //       child: Column(
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           if (studentData != null) ...[
// //             Card(
// //               child: Padding(
// //                 padding: const EdgeInsets.all(16),
// //                 child: Column(
// //                   crossAxisAlignment: CrossAxisAlignment.start,
// //                   children: [
// //                     Text(
// //                       'Student Information',
// //                       style: AppTheme.headingMedium,
// //                     ),
// //                     const SizedBox(height: 16),
// //                     _buildInfoRow('Name', '${studentData['first_name'] ?? ''} ${studentData['last_name'] ?? ''}'),
// //                     _buildInfoRow('Student ID', studentData['id']?.toString() ?? 'N/A'),
// //                     _buildInfoRow('Email', studentData['student_email'] ?? 'N/A'),
// //                     _buildInfoRow('Phone', studentData['phone'] ?? 'N/A'),
// //                   ],
// //                 ),
// //               ),
// //             ),
// //             const SizedBox(height: 16),
// //             // Quick Actions
// //             Card(
// //               child: Padding(
// //                 padding: const EdgeInsets.all(16),
// //                 child: Column(
// //                   crossAxisAlignment: CrossAxisAlignment.start,
// //                   children: [
// //                     Text(
// //                       'Quick Actions',
// //                       style: AppTheme.headingMedium,
// //                     ),
// //                     const SizedBox(height: 16),
// //                     Row(
// //                       children: [
// //                         Expanded(
// //                           child: ElevatedButton.icon(
// //                             onPressed: () {
// //                               AppLogger.userAction('Navigate to attendance', {'from': 'parent_dashboard'});
// //                               context.go('/dashboard/parent/student_attendance');
// //                             },
// //                             icon: const Icon(Icons.event_available),
// //                             label: const Text('View Attendance'),
// //                           ),
// //                         ),
// //                         const SizedBox(width: 16),
// //                         Expanded(
// //                           child: ElevatedButton.icon(
// //                             onPressed: () {
// //                               AppLogger.userAction('Navigate to student info', {'from': 'parent_dashboard'});
// //                               context.go('/dashboard/parent/student_info');
// //                             },
// //                             icon: const Icon(Icons.info),
// //                             label: const Text('Student Info'),
// //                           ),
// //                         ),
// //                       ],
// //                     ),
// //                   ],
// //                 ),
// //               ),
// //             ),
// //           ] else ...[
// //             const Card(
// //               child: Padding(
// //                 padding: EdgeInsets.all(16),
// //                 child: Column(
// //                   children: [
// //                     Icon(Icons.info_outline, size: 48, color: Colors.grey),
// //                     SizedBox(height: 16),
// //                     Text(
// //                       'No Student Information Available',
// //                       style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
// //                     ),
// //                     SizedBox(height: 8),
// //                     Text(
// //                       'Student information is not available at this time.',
// //                       textAlign: TextAlign.center,
// //                     ),
// //                   ],
// //                 ),
// //               ),
// //             ),
// //           ],
// //         ],
// //       ),
// //     );
// //   }

// //   Widget _buildFinancialTab(Parent parent) {
// //     return SingleChildScrollView(
// //       padding: const EdgeInsets.all(16),
// //       child: Column(
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           Card(
// //             child: Padding(
// //               padding: const EdgeInsets.all(16),
// //               child: Column(
// //                 crossAxisAlignment: CrossAxisAlignment.start,
// //                 children: [
// //                   Text(
// //                     'Financial Information',
// //                     style: AppTheme.headingMedium,
// //                   ),
// //                   const SizedBox(height: 16),
// //                   _buildInfoRow(
// //                     'Annual Income',
// //                     parent.annualIncome != null
// //                         ? '₹${NumberFormat.compact().format(parent.annualIncome)}'
// //                         : 'Not specified',
// //                   ),
// //                   _buildInfoRow('Status', parent.annualIncome != null ? 'Verified' : 'Pending'),
// //                 ],
// //               ),
// //             ),
// //           ),
// //           const SizedBox(height: 16),
// //           Card(
// //             child: Padding(
// //               padding: const EdgeInsets.all(16),
// //               child: Column(
// //                 crossAxisAlignment: CrossAxisAlignment.start,
// //                 children: [
// //                   Text(
// //                     'Financial Tips',
// //                     style: AppTheme.headingMedium,
// //                   ),
// //                   const SizedBox(height: 16),
// //                   _buildTipItem('Consider setting up an education fund for your child\'s future'),
// //                   _buildTipItem('Explore tax benefits available for education expenses'),
// //                   _buildTipItem('Review school fee payment plans for better budgeting'),
// //                 ],
// //               ),
// //             ),
// //           ),
// //         ],
// //       ),
// //     );
// //   }

// //   Widget _buildStatCard({
// //     required IconData icon,
// //     required String title,
// //     required String value,
// //     required Color color,
// //   }) {
// //     return Card(
// //       child: Padding(
// //         padding: const EdgeInsets.all(16),
// //         child: Column(
// //           mainAxisAlignment: MainAxisAlignment.center,
// //           children: [
// //             Icon(icon, size: 32, color: color),
// //             const SizedBox(height: 8),
// //             Text(
// //               title,
// //               style: AppTheme.bodySmall.copyWith(color: Colors.grey),
// //               textAlign: TextAlign.center,
// //             ),
// //             const SizedBox(height: 4),
// //             Text(
// //               value,
// //               style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
// //               textAlign: TextAlign.center,
// //               maxLines: 2,
// //               overflow: TextOverflow.ellipsis,
// //             ),
// //           ],
// //         ),
// //       ),
// //     );
// //   }

// //   Widget _buildInfoRow(String label, String value) {
// //     return Padding(
// //       padding: const EdgeInsets.only(bottom: 12),
// //       child: Row(
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           SizedBox(
// //             width: 120,
// //             child: Text(
// //               label,
// //               style: AppTheme.bodyMedium.copyWith(
// //                 fontWeight: FontWeight.w500,
// //                 color: Colors.grey[600],
// //               ),
// //             ),
// //           ),
// //           Expanded(
// //             child: Text(
// //               value,
// //               style: AppTheme.bodyMedium,
// //             ),
// //           ),
// //         ],
// //       ),
// //     );
// //   }

// //   Widget _buildTipItem(String text) {
// //     return Padding(
// //       padding: const EdgeInsets.only(bottom: 12),
// //       child: Row(
// //         crossAxisAlignment: CrossAxisAlignment.start,
// //         children: [
// //           const Icon(Icons.lightbulb_outline, size: 20, color: Colors.amber),
// //           const SizedBox(width: 12),
// //           Expanded(
// //             child: Text(
// //               text,
// //               style: AppTheme.bodyMedium,
// //             ),
// //           ),
// //         ],
// //       ),
// //     );
// //   }
// // }

// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:intl/intl.dart';
// import 'dart:ui';
// import 'package:go_router/go_router.dart';
// import '../../core/services/token_service.dart';
// import '../../core/services/api_service.dart';
// import '../../core/utils/logger.dart';
// import '../../core/theme/app_theme.dart';
// import '../../core/widgets/base_page.dart';

// // Parent Data Model
// class Parent {
//   final String username;
//   final String firstName;
//   final String lastName;
//   final String email;
//   final String phone;
//   final String occupation;
//   final String relationship;
//   final String createdAt;
//   final double? annualIncome;
//   final String studentId;
//   final bool isActive;
//   final Map<String, dynamic>? student;

//   Parent({
//     required this.username,
//     required this.firstName,
//     required this.lastName,
//     required this.email,
//     required this.phone,
//     required this.occupation,
//     required this.relationship,
//     required this.createdAt,
//     this.annualIncome,
//     required this.studentId,
//     required this.isActive,
//     this.student,
//   });

//   factory Parent.fromJson(Map<String, dynamic> json) {
//     return Parent(
//       username: json['username'] ?? 'Unknown',
//       firstName: json['first_name'] ?? 'Unknown',
//       lastName: json['last_name'] ?? 'Unknown',
//       email: json['parent_email'] ?? 'Unknown',
//       phone: json['phone'] ?? 'Unknown',
//       occupation: json['occupation'] ?? 'Unknown',
//       relationship: json['relationship'] ?? 'Unknown',
//       createdAt: json['created_at'] ?? 'Unknown',
//       annualIncome: json['annual_income_inr']?.toDouble(),
//       studentId: json['student_id'] ?? 'Unknown',
//       isActive: json['is_active'] ?? false,
//       student: json['student'],
//     );
//   }
// }

// // Parent Provider for state management
// class ParentProvider with ChangeNotifier {
//   Parent? _parent;
//   bool _isLoading = true;
//   bool _isError = false;
//   String _errorMessage = '';

//   Parent? get parent => _parent;
//   bool get isLoading => _isLoading;
//   bool get isError => _isError;
//   String get errorMessage => _errorMessage;

//   final TokenService _tokenService = TokenService();
//   final ApiService _apiService = ApiService();

//   Future<void> fetchParentData() async {
//     _setLoading(true);
//     _setError(false, '');

//     try {
//       AppLogger.info('Fetching parent dashboard data');
//       final currentToken = await _tokenService.getToken();

//       if (currentToken == null || !(await _tokenService.isTokenValid())) {
//         AppLogger.warning('Token validation failed');
//         _setError(true, 'Authentication token has expired. Please log in again.');
//         return;
//       }

//       final data = await _apiService.fetchParentDashboard();
//       final parentData = Parent.fromJson(data['parent'] ?? data);

//       _parent = parentData;
//       _setLoading(false);
//       AppLogger.info('Parent dashboard data loaded successfully');
//     } catch (e) {
//       AppLogger.error('Failed to fetch parent data: $e');
//       String msg = 'Unable to fetch parent details. Please try again.';
//       if (e.toString().contains('401')) {
//         msg = 'Session expired. Please log in again.';
//       } else if (e.toString().contains('Network')) {
//         msg = 'Network error. Please check your connection.';
//       }
//       _setError(true, msg);
//       _setLoading(false);
//     }
//   }

//   Future<void> refreshData() async {
//     AppLogger.userAction('Parent dashboard refresh', {'timestamp': DateTime.now().toIso8601String()});
//     await fetchParentData();
//   }

//   void _setLoading(bool loading) {
//     _isLoading = loading;
//     notifyListeners();
//   }

//   void _setError(bool error, String message) {
//     _isError = error;
//     _errorMessage = message;
//     notifyListeners();
//   }

//   @override
//   void dispose() {
//     // No need to dispose of _parent, as it's just a reference.
//     // The provider is managed by ChangeNotifierProvider.value.
//     super.dispose();
//   }
// }

// // Parent Dashboard Page - Main widget for parent role dashboard
// class ParentDashboardPage extends StatefulWidget {
//   const ParentDashboardPage({super.key});

//   @override
//   State<ParentDashboardPage> createState() => _ParentDashboardPageState();
// }

// class _ParentDashboardPageState extends State<ParentDashboardPage>
//     with SingleTickerProviderStateMixin {
//   late TabController _tabController;
//   final ParentProvider _parentProvider = ParentProvider();

//   @override
//   void initState() {
//     super.initState();
//     AppLogger.navigation('Parent Dashboard', 'Page initialized');
//     _tabController = TabController(length: 3, vsync: this);
//     _parentProvider.fetchParentData();
//   }

//   @override
//   void dispose() {
//     _tabController.dispose();
//     _parentProvider.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return ChangeNotifierProvider.value(
//       value: _parentProvider,
//       child: Scaffold(
//         body: NestedScrollView(
//           headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
//             return <Widget>[
//               SliverAppBar(
//                 title: const Text('Parent Dashboard'),
//                 floating: true,
//                 pinned: true,
//                 snap: true,
//                 actions: [
//                   IconButton(
//                     onPressed: () {
//                       AppLogger.userAction('Parent dashboard refresh', {'source': 'app_bar'});
//                       _parentProvider.refreshData();
//                     },
//                     icon: const Icon(Icons.refresh),
//                     tooltip: 'Refresh',
//                   ),
//                 ],
//                 bottom: TabBar(
//                   controller: _tabController,
//                   tabs: const [
//                     Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
//                     Tab(icon: Icon(Icons.school), text: 'Student'),
//                     Tab(icon: Icon(Icons.attach_money), text: 'Financial'),
//                   ],
//                 ),
//               ),
//             ];
//           },
//           body: Consumer<ParentProvider>(
//             builder: (context, provider, child) {
//               if (provider.isLoading) {
//                 return _buildLoadingView();
//               }
//               if (provider.isError) {
//                 return _buildErrorView(provider);
//               }
//               return TabBarView(
//                 controller: _tabController,
//                 children: [
//                   _buildOverviewTab(provider.parent),
//                   _buildStudentTab(provider.parent),
//                   _buildFinancialTab(provider.parent),
//                 ],
//               );
//             },
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildLoadingView() {
//     return const Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           CircularProgressIndicator(),
//           SizedBox(height: 16),
//           Text('Loading parent dashboard...'),
//         ],
//       ),
//     );
//   }

//   Widget _buildErrorView(ParentProvider provider) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const Icon(Icons.error_outline, size: 64, color: Colors.red),
//           const SizedBox(height: 16),
//           Text(
//             'Error Loading Dashboard',
//             style: AppTheme.headingMedium.copyWith(color: Colors.red),
//           ),
//           const SizedBox(height: 8),
//           Text(
//             provider.errorMessage,
//             textAlign: TextAlign.center,
//             style: AppTheme.bodyMedium,
//           ),
//           const SizedBox(height: 16),
//           ElevatedButton.icon(
//             onPressed: () {
//               AppLogger.userAction('Parent dashboard retry', {'error': provider.errorMessage});
//               provider.refreshData();
//             },
//             icon: const Icon(Icons.refresh),
//             label: const Text('Retry'),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildDashboardContent(Parent? parent) {
//     if (parent == null) {
//       return const Center(child: Text('No parent data available'));
//     }

//     return TabBarView(
//       controller: _tabController,
//       children: [
//         _buildOverviewTab(parent),
//         _buildStudentTab(parent),
//         _buildFinancialTab(parent),
//       ],
//     );
//   }

//   Widget _buildOverviewTab(Parent? parent) {
//     if (parent == null) {
//       return const Center(child: Text('No parent data available'));
//     }
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           _buildInfoCard(
//             title: 'Welcome, ${parent.firstName} ${parent.lastName}!',
//             subtitle: 'Here\'s an overview of your account and your child\'s progress.',
//             color: Colors.blue.withOpacity(0.1),
//             icon: Icons.waving_hand,
//           ),
//           const SizedBox(height: 16),
//           Text('Quick Stats', style: AppTheme.headingSmall),
//           const SizedBox(height: 8),
//           GridView.count(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             crossAxisCount: 2,
//             crossAxisSpacing: 16,
//             mainAxisSpacing: 16,
//             childAspectRatio: 1.5,
//             children: [
//               _buildStatCard(
//                 icon: Icons.person,
//                 title: 'Parent',
//                 value: '${parent.firstName} ${parent.lastName}',
//                 color: Colors.blue,
//               ),
//               _buildStatCard(
//                 icon: Icons.phone,
//                 title: 'Contact',
//                 value: parent.phone,
//                 color: Colors.green,
//               ),
//               _buildStatCard(
//                 icon: Icons.email,
//                 title: 'Email',
//                 value: parent.email,
//                 color: Colors.orange,
//               ),
//               _buildStatCard(
//                 icon: Icons.work,
//                 title: 'Occupation',
//                 value: parent.occupation,
//                 color: Colors.purple,
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildStudentTab(Parent? parent) {
//     final studentData = parent?.student;
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (studentData != null) ...[
//             _buildInfoCard(
//               title: 'Student Information',
//               subtitle: 'View your child\'s details and perform quick actions.',
//               color: Colors.green.withOpacity(0.1),
//               icon: Icons.school,
//             ),
//             const SizedBox(height: 16),
//             _buildSectionCard(
//               title: 'Student Details',
//               children: [
//                 _buildInfoRow('Name', '${studentData['first_name'] ?? ''} ${studentData['last_name'] ?? ''}'),
//                 _buildInfoRow('Student ID', studentData['id']?.toString() ?? 'N/A'),
//                 _buildInfoRow('Email', studentData['student_email'] ?? 'N/A'),
//                 _buildInfoRow('Phone', studentData['phone'] ?? 'N/A'),
//               ],
//             ),
//             const SizedBox(height: 16),
//             _buildSectionCard(
//               title: 'Quick Actions',
//               children: [
//                 Wrap(
//                   spacing: 16,
//                   runSpacing: 16,
//                   children: [
//                     _buildActionButton(
//                       icon: Icons.event_available,
//                       label: 'View Attendance',
//                       onPressed: () {
//                         AppLogger.userAction('Navigate to attendance', {'from': 'parent_dashboard'});
//                         context.go('/dashboard/parent/student_attendance');
//                       },
//                     ),
//                     _buildActionButton(
//                       icon: Icons.info,
//                       label: 'Student Info',
//                       onPressed: () {
//                         AppLogger.userAction('Navigate to student info', {'from': 'parent_dashboard'});
//                         context.go('/dashboard/parent/student_info');
//                       },
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ] else ...[
//             _buildInfoCard(
//               title: 'No Student Information',
//               subtitle: 'Student information is not available at this time.',
//               color: Colors.grey.withOpacity(0.1),
//               icon: Icons.info_outline,
//             ),
//           ],
//         ],
//       ),
//     );
//   }

//   Widget _buildFinancialTab(Parent? parent) {
//     if (parent == null) {
//       return const Center(child: Text('No parent data available'));
//     }
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           _buildInfoCard(
//             title: 'Financial Dashboard',
//             subtitle: 'Manage financial information and get useful tips.',
//             color: Colors.orange.withOpacity(0.1),
//             icon: Icons.attach_money,
//           ),
//           const SizedBox(height: 16),
//           _buildSectionCard(
//             title: 'Financial Information',
//             children: [
//               _buildInfoRow(
//                 'Annual Income',
//                 parent.annualIncome != null
//                     ? '₹${NumberFormat.compact().format(parent.annualIncome)}'
//                     : 'Not specified',
//               ),
//               _buildInfoRow('Status', parent.annualIncome != null ? 'Verified' : 'Pending'),
//             ],
//           ),
//           const SizedBox(height: 16),
//           _buildSectionCard(
//             title: 'Financial Tips',
//             children: [
//               _buildTipItem('Consider setting up an education fund for your child\'s future'),
//               _buildTipItem('Explore tax benefits available for education expenses'),
//               _buildTipItem('Review school fee payment plans for better budgeting'),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildInfoCard({
//     required String title,
//     required String subtitle,
//     required Color color,
//     required IconData icon,
//   }) {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: color,
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Icon(icon, size: 36, color: color.darken(0.3)),
//           const SizedBox(width: 16),
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(title, style: AppTheme.headingMedium),
//                 const SizedBox(height: 4),
//                 Text(subtitle, style: AppTheme.bodyMedium),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildSectionCard({
//     required String title,
//     required List<Widget> children,
//   }) {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(12),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 5),
//           ),
//         ],
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(title, style: AppTheme.headingSmall),
//           const SizedBox(height: 16),
//           ...children,
//         ],
//       ),
//     );
//   }

//   Widget _buildStatCard({
//     required IconData icon,
//     required String title,
//     required String value,
//     required Color color,
//   }) {
//     return Card(
//       elevation: 4,
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(icon, size: 32, color: color),
//             const SizedBox(height: 8),
//             Text(
//               title,
//               style: AppTheme.bodySmall.copyWith(color: Colors.grey[600]),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 4),
//             Text(
//               value,
//               style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
//               textAlign: TextAlign.center,
//               maxLines: 2,
//               overflow: TextOverflow.ellipsis,
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildInfoRow(String label, String value) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 8),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SizedBox(
//             width: 120,
//             child: Text(
//               label,
//               style: AppTheme.bodyMedium.copyWith(
//                 fontWeight: FontWeight.w500,
//                 color: Colors.grey[600],
//               ),
//             ),
//           ),
//           Expanded(
//             child: Text(
//               value,
//               style: AppTheme.bodyMedium,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildTipItem(String text) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 12),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const Icon(Icons.lightbulb_outline, size: 20, color: Colors.amber),
//           const SizedBox(width: 12),
//           Expanded(
//             child: Text(
//               text,
//               style: AppTheme.bodyMedium,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildActionButton({
//     required IconData icon,
//     required String label,
//     required VoidCallback onPressed,
//   }) {
//     return Expanded(
//       child: ElevatedButton.icon(
//         onPressed: onPressed,
//         icon: Icon(icon),
//         label: Text(label),
//         style: ElevatedButton.styleFrom(
//           padding: const EdgeInsets.symmetric(vertical: 16),
//           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
//         ),
//       ),
//     );
//   }
// }

// // Extension to darken colors
// extension ColorUtils on Color {
//   Color darken([double amount = .1]) {
//     assert(amount >= 0 && amount <= 1);
//     final hsl = HSLColor.fromColor(this);
//     final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
//     return hslDark.toColor();
//   }
// }

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';

// Parent Data Model
class Parent {
  final String username;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String occupation;
  final String relationship;
  final String createdAt;
  final double? annualIncome;
  final String studentId;
  final bool isActive;
  final Map<String, dynamic>? student;

  Parent({
    required this.username,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.occupation,
    required this.relationship,
    required this.createdAt,
    this.annualIncome,
    required this.studentId,
    required this.isActive,
    this.student,
  });

  factory Parent.fromJson(Map<String, dynamic> json) {
    return Parent(
      username: json['username'] ?? 'Unknown',
      firstName: json['first_name'] ?? 'Unknown',
      lastName: json['last_name'] ?? 'Unknown',
      email: json['parent_email'] ?? 'Unknown',
      phone: json['phone'] ?? 'Unknown',
      occupation: json['occupation'] ?? 'Unknown',
      relationship: json['relationship'] ?? 'Unknown',
      createdAt: json['created_at'] ?? 'Unknown',
      annualIncome: json['annual_income_inr']?.toDouble(),
      studentId: json['student_id'] ?? 'Unknown',
      isActive: json['is_active'] ?? false,
      student: json['student'],
    );
  }
}

// Parent Provider for state management
class ParentProvider with ChangeNotifier {
  Parent? _parent;
  bool _isLoading = true;
  bool _isError = false;
  String _errorMessage = '';

  Parent? get parent => _parent;
  bool get isLoading => _isLoading;
  bool get isError => _isError;
  String get errorMessage => _errorMessage;

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  Future<void> fetchParentData() async {
    _setLoading(true);
    _setError(false, '');

    try {
      AppLogger.info('Fetching parent dashboard data');
      final currentToken = await _tokenService.getToken();

      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        AppLogger.warning('Token validation failed');
        _setError(true, 'Authentication token has expired. Please log in again.');
        return;
      }

      final data = await _apiService.fetchParentDashboard();
      final parentData = Parent.fromJson(data['parent'] ?? data);

      _parent = parentData;
      _setLoading(false);
      AppLogger.info('Parent dashboard data loaded successfully');
    } catch (e) {
      AppLogger.error('Failed to fetch parent data: $e');
      String msg = 'Unable to fetch parent details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      _setError(true, msg);
      _setLoading(false);
    }
  }

  Future<void> refreshData() async {
    AppLogger.userAction('Parent dashboard refresh', {'timestamp': DateTime.now().toIso8601String()});
    await fetchParentData();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool error, String message) {
    _isError = error;
    _errorMessage = message;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}

// Parent Dashboard Page
class ParentDashboardPage extends StatefulWidget {
  const ParentDashboardPage({super.key});

  @override
  State<ParentDashboardPage> createState() => _ParentDashboardPageState();
}

class _ParentDashboardPageState extends State<ParentDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ParentProvider _parentProvider = ParentProvider();

  @override
  void initState() {
    super.initState();
    AppLogger.navigation('Parent Dashboard', 'Page initialized');
    _tabController = TabController(length: 3, vsync: this);
    _parentProvider.fetchParentData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _parentProvider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _parentProvider,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              SliverAppBar(
                title: Text(
                  'Parent Dashboard',
                  style: Theme.of(context).textTheme.headlineSmall ??
                      const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                ),
                floating: true,
                pinned: true,
                snap: true,
                forceElevated: innerBoxIsScrolled,
                actions: [
                  IconButton(
                    onPressed: () {
                      AppLogger.userAction('Parent dashboard refresh', {'source': 'app_bar'});
                      _parentProvider.refreshData();
                    },
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Refresh',
                  ),
                ],
                bottom: TabBar(
                  controller: _tabController,
                  indicatorColor: Theme.of(context).primaryColor,
                  labelColor: Theme.of(context).primaryColor,
                  unselectedLabelColor: Colors.grey,
                  tabs: const [
                    Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
                    Tab(icon: Icon(Icons.school), text: 'Student'),
                    Tab(icon: Icon(Icons.attach_money), text: 'Financial'),
                  ],
                ),
              ),
            ];
          },
          body: Consumer<ParentProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return _buildLoadingView(context);
              }
              if (provider.isError) {
                return _buildErrorView(context, provider);
              }
              return TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(context, provider.parent),
                  _buildStudentTab(context, provider.parent),
                  _buildFinancialTab(context, provider.parent),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Loading parent dashboard...',
            style: Theme.of(context).textTheme.bodyMedium ??
                const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Colors.black87,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, ParentProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error Loading Dashboard',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(color: Colors.red) ??
                  const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              provider.errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium ??
                  const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: Colors.black87,
                  ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                AppLogger.userAction('Parent dashboard retry', {'error': provider.errorMessage});
                provider.refreshData();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab(BuildContext context, Parent? parent) {
    if (parent == null) {
      return Center(
        child: Text(
          'No parent data available',
          style: Theme.of(context).textTheme.bodyMedium ??
              const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Colors.black87,
              ),
        ),
      );
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoCard(
                context: context,
                title: 'Welcome, ${parent.firstName} ${parent.lastName}!',
                subtitle: 'Here\'s an overview of your account and your child\'s progress.',
                color: Colors.blue.withOpacity(0.1),
                icon: Icons.waving_hand,
              ),
              const SizedBox(height: 16),
              Text(
                'Quick Stats',
                style: Theme.of(context).textTheme.headlineSmall ??
                    const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
              ),
              const SizedBox(height: 8),
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: constraints.maxWidth > 600 ? 4 : 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: constraints.maxWidth > 600 ? 1.8 : 1.5,
                children: [
                  _buildStatCard(
                    context: context,
                    icon: Icons.person,
                    title: 'Parent',
                    value: '${parent.firstName} ${parent.lastName}',
                    color: Colors.blue,
                  ),
                  _buildStatCard(
                    context: context,
                    icon: Icons.phone,
                    title: 'Contact',
                    value: parent.phone,
                    color: Colors.green,
                  ),
                  _buildStatCard(
                    context: context,
                    icon: Icons.email,
                    title: 'Email',
                    value: parent.email,
                    color: Colors.orange,
                  ),
                  _buildStatCard(
                    context: context,
                    icon: Icons.work,
                    title: 'Occupation',
                    value: parent.occupation,
                    color: Colors.purple,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStudentTab(BuildContext context, Parent? parent) {
    final studentData = parent?.student;
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (studentData != null) ...[
                _buildInfoCard(
                  context: context,
                  title: 'Student Information',
                  subtitle: 'View your child\'s details and perform quick actions.',
                  color: Colors.green.withOpacity(0.1),
                  icon: Icons.school,
                ),
                const SizedBox(height: 16),
                _buildSectionCard(
                  context: context,
                  title: 'Student Details',
                  children: [
                    _buildInfoRow(
                      context,
                      'Name',
                      '${studentData['first_name'] ?? ''} ${studentData['last_name'] ?? ''}',
                    ),
                    _buildInfoRow(context, 'Student ID', studentData['id']?.toString() ?? 'N/A'),
                    _buildInfoRow(context, 'Email', studentData['student_email'] ?? 'N/A'),
                    _buildInfoRow(context, 'Phone', studentData['phone'] ?? 'N/A'),
                  ],
                ),
                const SizedBox(height: 16),
                _buildSectionCard(
                  context: context,
                  title: 'Quick Actions',
                  children: [
                    Wrap(
                      spacing: 16,
                      runSpacing: 16,
                      children: [
                        _buildActionButton(
                          context: context,
                          icon: Icons.event_available,
                          label: 'View Attendance',
                          onPressed: () {
                            AppLogger.userAction('Navigate to attendance', {'from': 'parent_dashboard'});
                            context.go('/dashboard/parent/student_attendance');
                          },
                        ),
                        _buildActionButton(
                          context: context,
                          icon: Icons.info,
                          label: 'Student Info',
                          onPressed: () {
                            AppLogger.userAction('Navigate to student info', {'from': 'parent_dashboard'});
                            context.go('/dashboard/parent/student_info');
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ] else ...[
                _buildInfoCard(
                  context: context,
                  title: 'No Student Information',
                  subtitle: 'Student information is not available at this time.',
                  color: Colors.grey.withOpacity(0.1),
                  icon: Icons.info_outline,
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildFinancialTab(BuildContext context, Parent? parent) {
    if (parent == null) {
      return Center(
        child: Text(
          'No parent data available',
          style: Theme.of(context).textTheme.bodyMedium ??
              const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Colors.black87,
              ),
        ),
      );
    }
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            context: context,
            title: 'Financial Dashboard',
            subtitle: 'Manage financial information and get useful tips.',
            color: Colors.orange.withOpacity(0.1),
            icon: Icons.attach_money,
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context: context,
            title: 'Financial Information',
            children: [
              _buildInfoRow(
                context,
                'Annual Income',
                parent.annualIncome != null
                    ? '₹${NumberFormat.compact().format(parent.annualIncome)}'
                    : 'Not specified',
              ),
              _buildInfoRow(context, 'Status', parent.annualIncome != null ? 'Verified' : 'Pending'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context: context,
            title: 'Financial Tips',
            children: [
              _buildTipItem(context, 'Consider setting up an education fund for your child\'s future'),
              _buildTipItem(context, 'Explore tax benefits available for education expenses'),
              _buildTipItem(context, 'Review school fee payment plans for better budgeting'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 36, color: color.darken(0.3)),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.headlineMedium ??
                      const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodyMedium ??
                      const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black87,
                      ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required BuildContext context,
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall ??
                const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[600]) ??
                  TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold) ??
                    const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                      ) ??
                  TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium ??
                  const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: Colors.black87,
                  ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTipItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.lightbulb_outline, size: 20, color: Colors.amber),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium ??
                  const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: Colors.black87,
                  ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(
          label,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }
}

// Extension to darken colors
extension ColorUtils on Color {
  Color darken([double amount = .1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(this);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}