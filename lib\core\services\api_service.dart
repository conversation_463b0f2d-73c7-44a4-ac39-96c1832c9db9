/// api_service.dart - HTTP API Communication Service
///
/// English: This service handles all HTTP API communications for the Sasthra application.
/// It provides a centralized interface for making API calls to the backend server, including
/// authentication endpoints, data fetching, and CRUD operations. The service includes
/// automatic token management, request/response interceptors, error handling, and network
/// connectivity checks for robust API communication.
///
/// Tanglish: Inga naama app oda backend server oda communicate panna vendiya API service
/// irukku. Authentication, data fetching, CRUD operations - ella API calls um inga handle
/// pannum. Automatic token management, error handling, network connectivity check - ellam
/// irukku robust communication ku.
///
/// Key Features:
/// - Centralized HTTP client with Dio framework
/// - Automatic authentication token management
/// - Request/Response interceptors for logging and processing
/// - Network connectivity monitoring
/// - Comprehensive error handling and custom exceptions
/// - Retry mechanism for failed requests
/// - Request timeout and connection management
/// - Pretty logging for development debugging
///
/// API Endpoints:
/// - Authentication: login, OTP verification, token refresh
/// - User data: profile, preferences, settings
/// - Menu data: role-based navigation menus
/// - Application data: courses, centers, faculty, students
///
/// Usage: ApiService().login(username, password), ApiService().fetchMenuData()
library;

import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:developer';
import 'package:sasthra/core/utils/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

import '../config/app_config.dart';
import '../utils/logger.dart';
import '../models/auth_models.dart';
import '../models/menu_models.dart';
import '../exceptions/api_exceptions.dart';
import 'token_service.dart';

/// ApiService - HTTP API Communication Management Class
///
/// English: Singleton service that manages all HTTP API communications with the backend.
/// Tanglish: Backend oda communicate panna vendiya singleton API service class.
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal() {
    _initializeDio();
  }

  late Dio _dio;
  final TokenService _tokenService = TokenService();
    static String? _userId;

  static String? get userId => _userId;

  static void setUserId(String id) {
    _userId = id;
    AppLogger.info("User ID set: $id");
  }


  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: AppConfig.connectionTimeout,
      receiveTimeout: AppConfig.apiTimeout,
      sendTimeout: AppConfig.apiTimeout,
      headers: AppConfig.defaultHeaders,
    ));

    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor(_tokenService));
    _dio.interceptors.add(_ErrorInterceptor());
    
    if (AppConfig.isDebug) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
      ));
    }
  }

  /// Check network connectivity
  Future<bool> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      AppLogger.error('Connectivity check failed: $e');
      return false;
    }
  }

  /// Login with username and password
  Future<LoginResponse> login(String username, String password) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.post(
        AppConfig.loginEndpoint,
        data: {
          'username': username,
          'password': password,
        },
      );

      return LoginResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Login error: $e');
      throw GenericApiException('Login failed: ${e.toString()}');
    }
  }

  /// Verify OTP
  Future<AuthResponse> verifyOtp(String otp) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.post(
        AppConfig.verifyOtpEndpoint,
        data: {'otp': otp},
      );

      return AuthResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('OTP verification error: $e');
      throw GenericApiException('OTP verification failed: ${e.toString()}');
    }
  }

  /// Validate token with backend
  Future<bool> validateToken(String token) async {
    try {
      if (!await _checkConnectivity()) {
        return false;
      }

      final response = await _dio.post(
        AppConfig.verifyTokenEndpoint,
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      return response.statusCode == 200 && response.data['valid'] == true;
    } on DioException catch (e) {
      AppLogger.warning('Token validation failed: ${e.message}');
      return false;
    } catch (e) {
      AppLogger.error('Token validation error: $e');
      return false;
    }
  }

  /// Validate session
  Future<bool> validateSession(String userId, String sessionId) async {
    try {
      if (!await _checkConnectivity()) {
        return false;
      }

      final response = await _dio.post(
        AppConfig.validateSessionEndpoint,
        data: {
          'user_id': userId,
          'active_session_id': sessionId,
        },
      );

      return response.statusCode == 200;
    } on DioException catch (e) {
      AppLogger.warning('Session validation failed: ${e.message}');
      return false;
    } catch (e) {
      AppLogger.error('Session validation error: $e');
      return false;
    }
  }

  /// Get role-based menu
  Future<List<MenuItem>> getMenu(String role) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.get('${AppConfig.menuEndpoint}/$role');

      final List<dynamic> menuData = response.data;
      return menuData.map((item) => MenuItem.fromJson(item)).toList();
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Get menu error: $e');
      throw GenericApiException('Failed to load menu: ${e.toString()}');
    }
  }

  /// Alias for getMenu to match dashboard usage
  Future<List<MenuItem>> getMenuItems(String role) async {
    return getMenu(role);
  }

  /// Generic GET request method
  Future<Response> get(String endpoint, {Map<String, String>? headers}) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      AppLogger.info('🌐 API: Making GET request to $endpoint');

      final response = await _dio.get(
        endpoint,
        options: Options(headers: headers),
      );

      AppLogger.info('🌐 API: GET request successful - Status: ${response.statusCode}');
      return response;
    } on DioException catch (e) {
      AppLogger.error('🌐 API: GET request failed to $endpoint: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('🌐 API: Unexpected error in GET request to $endpoint: $e');
      throw GenericApiException('Request failed: ${e.toString()}');
    }
  }





  /// Fetch student dashboard data
  Future<Map<String, dynamic>> fetchStudentDashboard() async {
    try {
      print('🌐 API DEBUG: Starting fetchStudentDashboard...');
      AppLogger.info('🌐 API: Starting fetchStudentDashboard request');

      if (!await _checkConnectivity()) {
        print('🌐 API DEBUG: No internet connection');
        throw NetworkException('No internet connection');
      }
      print('🌐 API DEBUG: Internet connection verified');

      // Check if we have a token before making the request
      final token = await _tokenService.getToken();
      print('🌐 API DEBUG: Token exists for request: ${token != null}');
      print('🌐 API DEBUG: Token length: ${token?.length ?? 0}');
      AppLogger.info('🌐 API: Token available for request: ${token != null}');

      AppLogger.info('🌐 API: Making GET request to /student-dashboard');
      print('🌐 API DEBUG: Making GET request to /student-dashboard');

      final response = await _dio.get('/student-dashboard');

      print('🌐 API DEBUG: Response received - Status: ${response.statusCode}');
      print('🌐 API DEBUG: Response headers: ${response.headers.map}');
      print('🌐 API DEBUG: Response data type: ${response.data.runtimeType}');
      print('🌐 API DEBUG: Response data: ${response.data}');

      AppLogger.info('🌐 API: Student dashboard data fetched successfully');
      AppLogger.info('🌐 API: Response status: ${response.statusCode}');
      AppLogger.info('🌐 API: Response data keys: ${response.data is Map ? (response.data as Map).keys.toList() : 'Not a Map'}');

      return response.data;
    } on DioException catch (e) {
      print('🌐 API DEBUG: DioException caught - Type: ${e.type}');
      print('🌐 API DEBUG: DioException message: ${e.message}');
      print('🌐 API DEBUG: DioException response: ${e.response?.data}');
      print('🌐 API DEBUG: DioException status code: ${e.response?.statusCode}');

      AppLogger.error('🌐 API: Failed to fetch student dashboard: ${e.message}');
      AppLogger.error('🌐 API: Error type: ${e.type}');
      AppLogger.error('🌐 API: Status code: ${e.response?.statusCode}');
      throw _handleDioException(e);
    } catch (e) {
      print('🌐 API DEBUG: Unexpected exception: $e');
      print('🌐 API DEBUG: Exception type: ${e.runtimeType}');
      AppLogger.error('🌐 API: Unexpected error fetching student dashboard: $e');
      throw GenericApiException('Failed to fetch student dashboard: ${e.toString()}');
    }
  }

  /// Fetch parent dashboard data
  Future<Map<String, dynamic>> fetchParentDashboard() async {
    try {
      print('🌐 API DEBUG: Starting fetchParentDashboard...');
      AppLogger.info('🌐 API: Starting fetchParentDashboard request');

      if (!await _checkConnectivity()) {
        print('🌐 API DEBUG: No internet connection');
        throw NetworkException('No internet connection');
      }
      print('🌐 API DEBUG: Internet connection verified');

      // Check if we have a token before making the request
      final token = await _tokenService.getToken();
      print('🌐 API DEBUG: Token exists for request: ${token != null}');
      print('🌐 API DEBUG: Token length: ${token?.length ?? 0}');
      AppLogger.info('🌐 API: Token available for request: ${token != null}');

      AppLogger.info('🌐 API: Making GET request to ${AppConfig.parentDashboardEndpoint}');
      print('🌐 API DEBUG: Making GET request to ${AppConfig.parentDashboardEndpoint}');

      final response = await _dio.get(AppConfig.parentDashboardEndpoint);

      print('🌐 API DEBUG: Response status code: ${response.statusCode}');
      print('🌐 API DEBUG: Response data type: ${response.data.runtimeType}');
      print('🌐 API DEBUG: Response data: ${response.data}');
      AppLogger.info('🌐 API: Successfully fetched parent dashboard data');
      AppLogger.info('🌐 API: Response status: ${response.statusCode}');

      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      print('🌐 API DEBUG: DioException caught: ${e.message}');
      print('🌐 API DEBUG: DioException type: ${e.type}');
      print('🌐 API DEBUG: Response status code: ${e.response?.statusCode}');
      print('🌐 API DEBUG: Response data: ${e.response?.data}');

      AppLogger.error('🌐 API: Failed to fetch parent dashboard: ${e.message}');
      AppLogger.error('🌐 API: Error type: ${e.type}');
      AppLogger.error('🌐 API: Status code: ${e.response?.statusCode}');
      throw _handleDioException(e);
    } catch (e) {
      print('🌐 API DEBUG: Unexpected exception: $e');
      print('🌐 API DEBUG: Exception type: ${e.runtimeType}');
      AppLogger.error('🌐 API: Unexpected error fetching parent dashboard: $e');
      throw GenericApiException('Failed to fetch parent dashboard: ${e.toString()}');
    }
  }

  /// Validate session and refresh token
  Future<Map<String, dynamic>> validateSessionAndRefreshToken() async {
    try {
      print('🔐 API DEBUG: Starting validateSessionAndRefreshToken...');
      AppLogger.info('🔐 API: Starting session validation and token refresh');

      if (!await _checkConnectivity()) {
        print('🔐 API DEBUG: No internet connection');
        throw NetworkException('No internet connection');
      }
      print('🔐 API DEBUG: Internet connection verified');

      AppLogger.info('🔐 API: Making POST request to /validate-session');
      print('🔐 API DEBUG: Making POST request to /validate-session');

      final response = await _dio.post('/validate-session');

      print('🔐 API DEBUG: Response received - Status: ${response.statusCode}');
      print('🔐 API DEBUG: Response headers: ${response.headers.map}');
      print('🔐 API DEBUG: Response data type: ${response.data.runtimeType}');
      print('🔐 API DEBUG: Response data: ${response.data}');

      // Check for token in response
      final responseData = response.data as Map<String, dynamic>;
      final token = responseData['token'] ?? responseData['access_token'] ?? responseData['jwt_token'];
      print('🔐 API DEBUG: Token found in response: ${token != null}');
      if (token != null) {
        print('🔐 API DEBUG: Token length: ${token.toString().length}');
        final tokenPreview = token.toString().length > 20
            ? '${token.toString().substring(0, 10)}...${token.toString().substring(token.toString().length - 10)}'
            : token.toString();
        print('🔐 API DEBUG: Token preview: $tokenPreview');
      }

      AppLogger.info('🔐 API: Session validated and token refreshed successfully');
      AppLogger.info('🔐 API: Response status: ${response.statusCode}');
      AppLogger.info('🔐 API: Token present in response: ${token != null}');

      return response.data;
    } on DioException catch (e) {
      print('🔐 API DEBUG: DioException caught - Type: ${e.type}');
      print('🔐 API DEBUG: DioException message: ${e.message}');
      print('🔐 API DEBUG: DioException response: ${e.response?.data}');
      print('🔐 API DEBUG: DioException status code: ${e.response?.statusCode}');

      AppLogger.error('🔐 API: Failed to validate session: ${e.message}');
      AppLogger.error('🔐 API: Error type: ${e.type}');
      AppLogger.error('🔐 API: Status code: ${e.response?.statusCode}');
      throw _handleDioException(e);
    } catch (e) {
      print('🔐 API DEBUG: Unexpected exception: $e');
      print('🔐 API DEBUG: Exception type: ${e.runtimeType}');
      AppLogger.error('🔐 API: Unexpected error validating session: $e');
      throw GenericApiException('Failed to validate session: ${e.toString()}');
    }
  }

  /// Logout
  Future<void> logout(String userId) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      await _dio.post(
        AppConfig.logoutEndpoint,
        data: {'user_id': userId},
      );
    } on DioException catch (e) {
      AppLogger.warning('Logout request failed: ${e.message}');
      // Don't throw error for logout, just log it
    } catch (e) {
      AppLogger.error('Logout error: $e');
      // Don't throw error for logout, just log it
    }
  }



   Future<List<Map<String, dynamic>>> getThreads() async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.get(AppConfig.getThreadsEndpoint);
      final data = response.data;
      return (data is List)
          ? data.cast<Map<String, dynamic>>()
          : (data['data'] as List? ?? []).cast<Map<String, dynamic>>();
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Get threads error: $e');
      throw GenericApiException('Failed to load threads: ${e.toString()}');
    }
  }

  /// Get all community images (Gallery list)
  Future<List<Map<String, dynamic>>> getCommunityImages() async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.get(AppConfig.getCommunityImagesEndpoint);
      final data = response.data;
      return (data is List)
          ? data.cast<Map<String, dynamic>>()
          : (data['data'] as List? ?? []).cast<Map<String, dynamic>>();
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Get community images error: $e');
      throw GenericApiException('Failed to load images: ${e.toString()}');
    }
  }

  /// Get community image by ID with threads (GET)
  Future<Map<String, dynamic>> getCommunityImageById(String imageId) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final endpoint = '${AppConfig.getCommunityImageByIdEndpoint}/$imageId';
      final response = await _dio.get(endpoint);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Get community image by ID error: $e');
      throw GenericApiException('Failed to load image details: ${e.toString()}');
    }
  }

  /// Create new thread (POST)
  Future<Map<String, dynamic>> createThread(String content) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.post(
        AppConfig.createThreadEndpoint,
        data: {'content': content},
      );
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Create thread error: $e');
      throw GenericApiException('Failed to create thread: ${e.toString()}');
    }
  }

  /// Add reply to thread (POST)
  Future<Map<String, dynamic>> addReply(String content, String parentId) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.post(
        AppConfig.addReplyEndpoint,
        data: {
          'content': content,
          'parent_id': parentId,
        },
      );
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Add reply error: $e');
      throw GenericApiException('Failed to add reply: ${e.toString()}');
    }
  }

  /// Upload community image (POST multipart)
  Future<Map<String, dynamic>> uploadCommunityImage(
      File imageFile, String userId, {String? description}) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final formData = FormData.fromMap({
        'user_id': userId,
        if (description != null && description.isNotEmpty) 'description': description,
      });

      formData.files.add(MapEntry(
        'file',
        await MultipartFile.fromFile(
          imageFile.path,
          filename: imageFile.path.split('/').last,
        ),
      ));

      final response = await _dio.post(
        AppConfig.uploadCommunityImageEndpoint,
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          contentType: 'multipart/form-data',
        ),
      );
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Upload community image error: $e');
      throw GenericApiException('Failed to upload image: ${e.toString()}');
    }
  }

  /// Add thread to specific image (POST)
  Future<Map<String, dynamic>> addThreadToImage(
      String imageId, String content) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final endpoint = '${AppConfig.addThreadToImageEndpoint}/$imageId/thread';
      final response = await _dio.post(
        endpoint,
        data: {'content': content},
      );
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Add thread to image error: $e');
      throw GenericApiException(
          'Failed to add thread to image: ${e.toString()}');
    }
  }

  /// Add reply to image thread (POST)
  Future<Map<String, dynamic>> addReplyToImageThread(
      String imageId, String threadId, String content) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final endpoint =
          '${AppConfig.addReplyToImageThreadEndpoint}/$imageId/thread/$threadId/reply';
      final response = await _dio.post(
        endpoint,
        data: {'content': content},
      );
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Add reply to image thread error: $e');
      throw GenericApiException(
          'Failed to add reply to image thread: ${e.toString()}');
    }
  }


   Future<StudentDashboardResponse> getStudentDashboard() async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final response = await _dio.get(AppConfig.studentDashboardEndpoint);

      return StudentDashboardResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Student dashboard error: $e');
      throw GenericApiException(
          'Failed to load student dashboard: ${e.toString()}');
    }
  }

// In api_service.dart, add after getStudentDashboard
  Future<void> saveStudentDashboard(StudentDashboardResponse data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data.toJson());
      await prefs.setString(AppConfig.studentDashboardKey, jsonString);
    } catch (e) {
      AppLogger.error('Failed to save student dashboard: $e');
    }
  }

  Future<StudentDashboardResponse?> loadStudentDashboard() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(AppConfig.studentDashboardKey);
      if (jsonString != null) {
        return StudentDashboardResponse.fromJson(jsonDecode(jsonString));
      }
      return null;
    } catch (e) {
      AppLogger.error('Failed to load student dashboard: $e');
      return null;
    }
  }



 Future<Map<String, dynamic>> _performMultipartSearch({
    required String endpoint,
    required String userId,
    String? text,
    File? imageFile,
    File? audioFile,
    Map<String, String>? additionalFields,
  }) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final formDataMap = {
        'user_id': userId,
        if (text != null) 'text': text,
        ...?additionalFields,
      };

      final formData = FormData.fromMap(formDataMap);

      if (imageFile != null) {
        formData.files.add(MapEntry(
          'image',
          await MultipartFile.fromFile(imageFile.path, filename: imageFile.path.split('/').last),
        ));
      }
      if (audioFile != null) {
        formData.files.add(MapEntry(
          'audio',
          await MultipartFile.fromFile(audioFile.path, filename: audioFile.path.split('/').last),
        ));
      }

      final response = await _dio.post(
        endpoint,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );
      
      return response.data;

    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.error('Multipart search error at $endpoint: $e');
      throw GenericApiException('Request failed: ${e.toString()}');
    }
  }

  /// Solves a doubt using text, image, or audio.
  Future<Map<String, dynamic>> solveDoubt({
    required String text,
    required String mode,
    required String language,
    required String userId,
    File? imageFile,
    File? audioFile,
    bool reset = false,
    bool includeHistory = false,
  }) async {
    return _performMultipartSearch(
      endpoint: AppConfig.problemSolverEndpoint,
      text: text,
      userId: userId,
      imageFile: imageFile,
      audioFile: audioFile,
      additionalFields: {
        'mode': mode,
        'language': language,
        'reset': reset.toString(),
        'include_history': includeHistory.toString(),
      },
    );
  }

  /// Performs a YouTube search.
  Future<Map<String, dynamic>> youtubeSearch({
    required String text,
    required String userId,
    File? imageFile,
    File? audioFile,
  }) async {
    return _performMultipartSearch(
      endpoint: AppConfig.youtubeSearchEndpoint,
      text: text,
      userId: userId,
      imageFile: imageFile,
      audioFile: audioFile,
    );
  }

  /// Performs a web search.
  Future<Map<String, dynamic>> webSearch({
    required String text,
    required String userId,
    File? imageFile,
    File? audioFile,
  }) async {
    return _performMultipartSearch(
      endpoint: AppConfig.webSearchEndpoint,
      text: text,
      userId: userId,
      imageFile: imageFile,
      audioFile: audioFile,
    );
  }

 Future<Map<String, dynamic>> verifyFace({
    required String userId,
    required File imageFile,
  }) async {
    try {
      if (!await _checkConnectivity()) {
        throw const NetworkException('No internet connection');
      }

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.verifyFaceEndpoint}'),
      );
      request.headers.addAll(AppConfig.defaultHeaders);
      request.fields['user_id'] = userId;
      request.files.add(
        await http.MultipartFile.fromPath('image_file', imageFile.path),
      );

      final response = await request.send().timeout(AppConfig.apiTimeout);
      final responseBody = await response.stream.bytesToString();

      if (AppConfig.isDebug) {
        debugPrint(
            'API Request: ${AppConfig.apiBaseUrl}${AppConfig.verifyFaceEndpoint}');
        debugPrint('Status Code: ${response.statusCode}');
        debugPrint('Headers: ${response.headers}');
        debugPrint('Response Body: $responseBody');
      }

      if (response.statusCode == 200) {
        return jsonDecode(responseBody) as Map<String, dynamic>;
      } else {
        throw Exception('Failed to verify face: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('Verify face error: $e');
      throw Exception('Failed to verify face: $e');
    }
  }







  

  /// Handle Dio exceptions
  ApiException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const TimeoutException('Request timeout. Please try again.');
      
      case DioExceptionType.connectionError:
        return const NetworkException('Network connection failed. Please check your internet connection.');
      
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Unknown error occurred';
        
        switch (statusCode) {
          case 400:
            return BadRequestException(message);
          case 401:
            return UnauthorizedException(message);
          case 403:
            return ForbiddenException(message);
          case 404:
            return NotFoundException(message);
          case 500:
            return ServerException(message);
          default:
            return GenericApiException('HTTP $statusCode: $message');
        }
      
      case DioExceptionType.cancel:
        return const GenericApiException('Request was cancelled');
      
      default:
        return GenericApiException('An unexpected error occurred: ${e.message}');
    }
  }
}

/// Auth interceptor to add token to requests
class _AuthInterceptor extends Interceptor {
  final TokenService _tokenService;

  _AuthInterceptor(this._tokenService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Skip auth for login and OTP verification endpoints
    if (options.path.contains('/login') || options.path.contains('/verify-otp')) {
      return handler.next(options);
    }

    final token = await _tokenService.getToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired or invalid, try to refresh
      final isValid = await _tokenService.validateAndRefreshToken();
      if (!isValid) {
        // Redirect to login will be handled by the app
        AppLogger.warning('Token refresh failed, user needs to login again');
      }
    }
    handler.next(err);
  }
}

/// Error interceptor for logging
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppLogger.error(
      'API Error: ${err.requestOptions.method} ${err.requestOptions.path} - '
      'Status: ${err.response?.statusCode} - Message: ${err.message}',
    );
    handler.next(err);
  }
}



