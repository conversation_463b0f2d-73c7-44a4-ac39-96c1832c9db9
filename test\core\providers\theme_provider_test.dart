import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../lib/core/theme/app_theme.dart';
void main() {
  group('Role-Based Theme Color Tests', () {
    test('should verify updated color values match requirements', () {
      // Test that the colors match the exact hex values specified
      expect(AppTheme.parentColor, equals(const Color(0xFF6E2FCA))); // #6e2fca
      expect(AppTheme.counselorColor, equals(const Color(0xFFF4C430))); // #f4c430
      expect(AppTheme.traineeColor, equals(const Color(0xFFF59E0B))); // #f59e0b
      expect(AppTheme.studentColor, equals(const Color(0xFF2563EB))); // #2563eb
      expect(AppTheme.directorColor, equals(const Color(0xFF7D1E1C))); // #7d1e1c
      expect(AppTheme.teacherColor, equals(const Color(0xFF000080))); // #000080
      expect(AppTheme.mentorColor, equals(const Color(0xFF7C007C))); // #7c007c
    });

    test('should return correct role colors for different roles', () {
      expect(AppTheme.getRoleColor('parent'), equals(AppTheme.parentColor));
      expect(AppTheme.getRoleColor('counselor'), equals(AppTheme.counselorColor));
      expect(AppTheme.getRoleColor('center_counselor'), equals(AppTheme.counselorColor));
      expect(AppTheme.getRoleColor('trainee'), equals(AppTheme.traineeColor));
      expect(AppTheme.getRoleColor('student'), equals(AppTheme.studentColor));
      expect(AppTheme.getRoleColor('director'), equals(AppTheme.directorColor));
      expect(AppTheme.getRoleColor('teacher'), equals(AppTheme.teacherColor));
      expect(AppTheme.getRoleColor('faculty'), equals(AppTheme.teacherColor));
      expect(AppTheme.getRoleColor('kota_teacher'), equals(AppTheme.teacherColor));
      expect(AppTheme.getRoleColor('mentor'), equals(AppTheme.mentorColor));
      expect(AppTheme.getRoleColor('mendor'), equals(AppTheme.mentorColor));
    });

    test('should return correct light colors for different roles', () {
      expect(AppTheme.getRoleColorLight('parent'), equals(AppTheme.parentColorLight));
      expect(AppTheme.getRoleColorLight('counselor'), equals(AppTheme.counselorColorLight));
      expect(AppTheme.getRoleColorLight('trainee'), equals(AppTheme.traineeColorLight));
      expect(AppTheme.getRoleColorLight('student'), equals(AppTheme.studentColorLight));
      expect(AppTheme.getRoleColorLight('director'), equals(AppTheme.directorColorLight));
      expect(AppTheme.getRoleColorLight('teacher'), equals(AppTheme.teacherColorLight));
      expect(AppTheme.getRoleColorLight('mentor'), equals(AppTheme.mentorColorLight));
    });

    test('should return fallback colors for unknown roles', () {
      expect(AppTheme.getRoleColor('unknown'), equals(AppTheme.primaryColor));
      expect(AppTheme.getRoleColorLight('unknown'), equals(const Color(0xFFEEF2FF)));
    });

    test('should generate role-specific gradients', () {
      final studentGradient = AppTheme.getRoleGradient('student');
      expect(studentGradient.colors, contains(AppTheme.studentColor));
      expect(studentGradient.colors, contains(AppTheme.studentColorLight));

      final directorGradient = AppTheme.getRoleGradient('director');
      expect(directorGradient.colors, contains(AppTheme.directorColor));
      expect(directorGradient.colors, contains(AppTheme.directorColorLight));
    });

    test('should return correct role display names', () {
      expect(AppTheme.getRoleDisplayName('parent'), equals('Parent'));
      expect(AppTheme.getRoleDisplayName('counselor'), equals('Counselor'));
      expect(AppTheme.getRoleDisplayName('center_counselor'), equals('Counselor'));
      expect(AppTheme.getRoleDisplayName('trainee'), equals('Trainee'));
      expect(AppTheme.getRoleDisplayName('student'), equals('Student'));
      expect(AppTheme.getRoleDisplayName('director'), equals('Director'));
      expect(AppTheme.getRoleDisplayName('teacher'), equals('Teacher'));
      expect(AppTheme.getRoleDisplayName('faculty'), equals('Teacher'));
      expect(AppTheme.getRoleDisplayName('kota_teacher'), equals('Teacher'));
      expect(AppTheme.getRoleDisplayName('mentor'), equals('Mentor'));
      expect(AppTheme.getRoleDisplayName('mendor'), equals('Mentor'));
      expect(AppTheme.getRoleDisplayName('unknown'), equals('User'));
    });

    test('should generate role-specific themes', () {
      final studentTheme = AppTheme.getRoleTheme('student');
      expect(studentTheme.colorScheme.primary, equals(AppTheme.studentColor));
      expect(studentTheme.colorScheme.primaryContainer, equals(AppTheme.studentColorLight));

      final directorTheme = AppTheme.getRoleTheme('director');
      expect(directorTheme.colorScheme.primary, equals(AppTheme.directorColor));
      expect(directorTheme.colorScheme.primaryContainer, equals(AppTheme.directorColorLight));
    });
  });
}
