import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decode/jwt_decode.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'discussions_tab.dart';
import 'gallery_tab.dart';
import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';

// Models
class User {
  final String id;
  final String role;
  final String username;
  final String? type;

  User({required this.id, required this.role, required this.username, this.type});

  factory User.fromJwt(String token) {
    print('Decoding token: $token');
    final payload = Jwt.parseJwt(token);
    return User(
      id: payload['user_id']?.toString() ?? 'unknown',
      role: payload['role'] ?? 'student',
      username: payload['username'] ?? 'User',
      type: payload['role']?.toString(),
    );
  }
}

class Thread {
  final String id;
  final String content;
  final String senderId;
  final String senderType;
  final String firstName;
  final DateTime createdAt;
  List<Reply> subthreads;

  Thread({
    required this.id,
    required this.content,
    required this.senderId,
    required this.senderType,
    required this.firstName,
    required this.createdAt,
    List<Reply>? subthreads,
  }) : subthreads = subthreads ?? [];

  factory Thread.fromJson(Map<String, dynamic> json) {
    return Thread(
      id: json['id'].toString(),
      content: json['content'] ?? '',
      senderId: json['sender_id'] ?? '',
      senderType: json['sender_type'] ?? '',
      firstName: json['first_name'] ?? '',
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : DateTime.now(),
      subthreads: (json['subthreads'] as List<dynamic>? ?? [])
          .map((r) => Reply.fromJson(r as Map<String, dynamic>))
          .toList(),
    );
  }
}

class Reply {
  final String id;
  final String content;
  final String senderId;
  final String firstName;
  final String senderType;
  final DateTime createdAt;
  final String parentId;

  Reply({
    required this.id,
    required this.content,
    required this.senderId,
    required this.firstName,
    required this.senderType,
    required this.createdAt,
    required this.parentId,
  });

  factory Reply.fromJson(Map<String, dynamic> json) {
    return Reply(
      id: json['id'].toString(),
      content: json['content'] ?? '',
      senderId: json['sender_id'] ?? '',
      firstName: json['first_name'] ?? '',
      senderType: json['sender_type'] ?? '',
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : DateTime.now(),
      parentId: json['parent_id'] ?? '',
    );
  }
}

class ImageData {
  final String id;
  final String url;
  final String? description;
  final String? firstName;
  final String? senderType;
  final DateTime? createdAt;
  List<Thread>? threads;

  ImageData({
    required this.id,
    required this.url,
    this.description,
    this.firstName,
    this.senderType,
    this.createdAt,
    this.threads,
  });

  factory ImageData.fromJson(Map<String, dynamic> json) {
    String imageUrl = json['cloudfront_url'] ?? json['url'] ?? '';
    if (imageUrl.isNotEmpty && !imageUrl.startsWith('http')) {
      imageUrl = '${AppConfig.baseUrl}$imageUrl';
      print('Fixed relative image URL: $imageUrl');
    }
    return ImageData(
      id: json['id'].toString(),
      url: imageUrl,
      description: json['description'],
      firstName: json['first_name'],
      senderType: json['sender_type'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      threads: (json['threads'] as List<dynamic>? ?? [])
          .map((t) => Thread.fromJson(t as Map<String, dynamic>))
          .toList(),
    );
  }
}

// Community Provider
class CommunityProvider extends ChangeNotifier {
  String? token;
  User? user;
  List<Thread> localThreads = [];
  List<ImageData> images = [];
  ImageData? selectedImage;
  bool loadingThreads = false;
  bool loadingImages = false;
  bool loadingFullImage = false;
  String newMessage = '';
  String newReply = '';
  Thread? selectedThread;
  final ApiService _apiService = ApiService();

  CommunityProvider() {
    print('CommunityProvider initialized with baseUrl: ${AppConfig.baseUrl}');
    _loadToken();
  }

  Future<void> _loadToken() async {
    print('=== TOKEN LOADING DEBUG ===');
    final prefs = await SharedPreferences.getInstance();
    token = prefs.getString(AppConfig.tokenKey);
    print('Token loaded: $token');
    final role = prefs.getString('role');
    print('Role loaded: $role');
    if (token != null && token!.isNotEmpty) {
      try {
        user = User.fromJwt(token!);
        if (user!.id != 'unknown') {
          print('Valid token/user, fetching data');
          await _fetchThreads();
          await _fetchImages();
        } else {
          print('Invalid user from token, setting guest');
          user = User(id: 'guest', role: 'guest', username: 'Guest');
        }
        // Accept both student and mendor roles
        if (role != null && (role == 'student' || role == 'mendor')) {
          user = User(id: user!.id, role: role, username: user!.username, type: role);
        }
      } catch (e) {
        print('Error parsing token: $e');
        user = User(id: 'guest', role: 'guest', username: 'Guest');
      }
    } else {
      print('No token found, setting guest user');
      user = User(id: 'guest', role: 'guest', username: 'Guest');
    }
    notifyListeners();
  }

  Future<void> login(String inputToken) async {
    if (inputToken.trim().isEmpty) {
      print('Login error: Empty token');
      return;
    }
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.tokenKey, inputToken.trim());
    
    // Get current role from preferences or determine from context
    final currentRole = prefs.getString('role') ?? 'student';
    await prefs.setString('role', currentRole);
    
    token = inputToken.trim();
    print('Login token set: $token');
    try {
      user = User.fromJwt(token!);
      if (user!.id != 'unknown') {
        await _fetchThreads();
        await _fetchImages();
      } else {
        print('Login failed: Invalid token payload');
        user = User(id: 'guest', role: 'guest', username: 'Guest');
      }
    } catch (e) {
      print('Login error: $e');
      user = User(id: 'guest', role: 'guest', username: 'Guest');
    }
    notifyListeners();
  }

  Future<void> _fetchThreads() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      debugPrint('No internet, skipping fetchThreads');
      loadingThreads = false;
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('No internet connection'),
          action: SnackBarAction(label: 'Retry', onPressed: _fetchThreads),
        ),
      );
      notifyListeners();
      return;
    }
    if (user == null || user!.id == 'guest' || user!.id == 'unknown') {
      print('Skipping fetchThreads: Invalid user');
      loadingThreads = false;
      notifyListeners();
      return;
    }
    loadingThreads = true;
    notifyListeners();
    try {
      final threadsData = await _apiService.getThreads();
      localThreads = threadsData.map((t) => Thread.fromJson(t)).toList();
    } catch (e) {
      debugPrint('Fetch threads error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Failed to fetch threads'),
          action: SnackBarAction(label: 'Retry', onPressed: _fetchThreads),
        ),
      );
    }
    loadingThreads = false;
    notifyListeners();
  }

  Future<void> _fetchImages() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      debugPrint('No internet, skipping fetchImages');
      loadingImages = false;
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('No internet connection'),
          action: SnackBarAction(label: 'Retry', onPressed: _fetchImages),
        ),
      );
      notifyListeners();
      return;
    }
    if (user == null || user!.id == 'guest' || user!.id == 'unknown') {
      print('Skipping fetchImages: Invalid user');
      loadingImages = false;
      notifyListeners();
      return;
    }
    loadingImages = true;
    notifyListeners();
    try {
      final imagesData = await _apiService.getCommunityImages();
      images = imagesData.map((i) => ImageData.fromJson(i)).toList();
    } catch (e) {
      debugPrint('Fetch images error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Failed to fetch images'),
          action: SnackBarAction(label: 'Retry', onPressed: _fetchImages),
        ),
      );
    }
    loadingImages = false;
    notifyListeners();
  }

  Future<void> fetchFullImage(String imageId) async {
    loadingFullImage = true;
    notifyListeners();
    
    try {
      final imageData = await _apiService.getCommunityImageById(imageId);
      selectedImage = ImageData.fromJson(imageData);
      print('Fetched full image: ${selectedImage?.id} with ${selectedImage?.threads?.length ?? 0} threads');
    } catch (e) {
      debugPrint('Fetch full image error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Failed to load image details'),
          action: SnackBarAction(
            label: 'Retry', 
            onPressed: () => fetchFullImage(imageId)
          ),
        ),
      );
    }
    
    loadingFullImage = false;
    notifyListeners();
  }

  Future<void> createThread(String content) async {
    if (content.trim().isEmpty) return;
    final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
    final tempThread = Thread(
      id: tempId,
      content: content,
      senderId: user!.id,
      senderType: user!.role,
      firstName: user!.username,
      createdAt: DateTime.now(),
    );
    localThreads.add(tempThread);
    selectedThread = tempThread;
    notifyListeners();
    try {
      final threadData = await _apiService.createThread(content);
      final realThread = Thread.fromJson(threadData);
      localThreads.removeWhere((t) => t.id == tempId);
      localThreads.add(realThread);
      selectedThread = realThread;
      await _fetchThreads();
    } catch (e) {
      localThreads.removeWhere((t) => t.id == tempId);
      debugPrint('Create thread error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Failed to create thread'),
          action: SnackBarAction(label: 'Retry', onPressed: () => createThread(content)),
        ),
      );
    }
    notifyListeners();
  }

  Future<void> addReply(String content, String parentId) async {
    if (content.trim().isEmpty) return;
    
    print('Adding reply to thread: $parentId with content: $content');
    
    final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
    final tempReply = Reply(
      id: tempId,
      content: content,
      senderId: user!.id,
      firstName: user!.username,
      senderType: user!.role,
      createdAt: DateTime.now(),
      parentId: parentId,
    );

    // Update local threads immediately
    final threadIndex = localThreads.indexWhere((t) => t.id == parentId);
    if (threadIndex != -1) {
      localThreads[threadIndex].subthreads.add(tempReply);
      print('Added temp reply to local thread at index $threadIndex');
    }
    
    // Update selected thread if it matches
    if (selectedThread?.id == parentId) {
      selectedThread!.subthreads.add(tempReply);
      print('Added temp reply to selected thread');
    }
    
    notifyListeners(); // Critical: notify before API call

    try {
      final replyData = await _apiService.addReply(content, parentId);
      final realReply = Reply.fromJson(replyData);
      
      print('API reply successful, replacing temp reply with real reply');
      
      // Replace temp reply with real reply
      if (threadIndex != -1) {
        final replyIndex = localThreads[threadIndex].subthreads.indexWhere((r) => r.id == tempId);
        if (replyIndex != -1) {
          localThreads[threadIndex].subthreads[replyIndex] = realReply;
        }
      }
      
      if (selectedThread?.id == parentId) {
        final replyIndex = selectedThread!.subthreads.indexWhere((r) => r.id == tempId);
        if (replyIndex != -1) {
          selectedThread!.subthreads[replyIndex] = realReply;
        }
      }
      
      notifyListeners();
    } catch (e) {
      // Remove temp reply on error
      if (threadIndex != -1) {
        localThreads[threadIndex].subthreads.removeWhere((r) => r.id == tempId);
      }
      if (selectedThread?.id == parentId) {
        selectedThread!.subthreads.removeWhere((r) => r.id == tempId);
      }
      
      debugPrint('Add reply error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Failed to send reply'),
          action: SnackBarAction(
            label: 'Retry', 
            onPressed: () => addReply(content, parentId)
          ),
        ),
      );
      notifyListeners();
    }
  }

  Future<void> uploadImage(File file, {String? description}) async {
    try {
      await _apiService.uploadCommunityImage(file, user!.id, description: description);
      await _fetchImages();
    } catch (e) {
      debugPrint('Upload error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Upload failed'),
          action: SnackBarAction(label: 'Retry', onPressed: () => uploadImage(file, description: description)),
        ),
      );
    }
  }

  Future<void> addThreadToImage(String imageId, String content) async {
    if (content.trim().isEmpty || selectedImage?.id != imageId) return;
    
    final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
    final tempThread = Thread(
      id: tempId,
      content: content,
      senderId: user!.id,
      senderType: user!.role,
      firstName: user!.username,
      createdAt: DateTime.now(),
    );

    // Add to selected image immediately
    selectedImage!.threads ??= [];
    selectedImage!.threads!.add(tempThread);
    notifyListeners();

    try {
      await _apiService.addThreadToImage(imageId, content);
      // Refresh full image data
      await fetchFullImage(imageId);
    } catch (e) {
      // Remove temp thread on error
      selectedImage!.threads!.removeWhere((t) => t.id == tempId);
      debugPrint('Add thread to image error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Failed to add comment to image'),
          action: SnackBarAction(
            label: 'Retry', 
            onPressed: () => addThreadToImage(imageId, content)
          ),
        ),
      );
      notifyListeners();
    }
  }

  Future<void> addReplyToImageThread(String imageId, String threadId, String content) async {
    try {
      await _apiService.addReplyToImageThread(imageId, threadId, content);
      await fetchFullImage(imageId);
    } catch (e) {
      debugPrint('Add reply to image thread error: $e');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(
          content: Text('Failed to add reply to image thread'),
          action: SnackBarAction(label: 'Retry', onPressed: () => addReplyToImageThread(imageId, threadId, content)),
        ),
      );
    }
  }

  Future<void> downloadImage(String url, {String filename = 'community-image.jpg'}) async {
    if (url.isEmpty || !Uri.parse(url).hasScheme || !Uri.parse(url).hasAuthority) {
      debugPrint('Invalid URL for download (skipping): $url');
      ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
        SnackBar(content: Text('Invalid image URL')),
      );
      return;
    }
    final status = await Permission.storage.request();
    if (status.isGranted) {
      try {
        print('Downloading: $url');
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } catch (e) {
        debugPrint('Download error: $e');
        ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
          SnackBar(content: Text('Download failed')),
        );
      }
    } else {
      debugPrint('Storage permission denied');
    }
  }

  String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void setNewMessage(String value) {
    newMessage = value;
    notifyListeners();
  }

  void setNewReply(String value) {
    newReply = value;
    notifyListeners();
  }

  void setSelectedThread(Thread? thread) {
    selectedThread = thread;
    print('Selected thread: ${thread?.id} - ${thread?.content}');
    print('Thread has ${thread?.subthreads.length ?? 0} replies');
    notifyListeners();
  }

  void clearNewReply() {
    newReply = '';
    notifyListeners();
  }

  void setSelectedImage(String? id) {
    if (id == null) {
      selectedImage = null;
      notifyListeners();
      return;
    }
    
    // Find image in local list first
    final localImage = images.firstWhere(
      (img) => img.id == id,
      orElse: () => ImageData(id: id, url: ''),
    );
    
    selectedImage = localImage;
    notifyListeners();
    
    // Fetch full details
    fetchFullImage(id);
  }
}

// Main Widget
GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class StudentCommunityPage extends StatefulWidget {
  const StudentCommunityPage({Key? key}) : super(key: key);

  @override
  _StudentCommunityPageState createState() => _StudentCommunityPageState();
}

class _StudentCommunityPageState extends State<StudentCommunityPage> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (didPop) return;
        Navigator.pop(context);
      },
      child: ChangeNotifierProvider(
        create: (_) => CommunityProvider(),
        child: Consumer<CommunityProvider>(
          builder: (context, provider, _) {
            if (provider.token == null || provider.user == null || provider.user!.id == 'guest') {
              return LoginScreen(provider: provider);
            }
            return Scaffold(
              backgroundColor: Colors.grey[100],
              appBar: PreferredSize(
                preferredSize: Size.fromHeight(screenHeight * 0.08),
                child: HeaderWidget(user: provider.user!),
              ),
              body: Column(
                children: [
                  TabSection(provider: provider, tabController: _tabController),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        DiscussionsTab(provider: provider),
                        GalleryTab(provider: provider),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

// Login Screen
class LoginScreen extends StatelessWidget {
  final CommunityProvider provider;

  const LoginScreen({Key? key, required this.provider}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TextEditingController controller = TextEditingController();
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 360;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFE8F0FE), Color(0xFFC5CAE9)],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 24),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: screenWidth * 0.85,
                ),
                child: Container(
                  padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.lock_outline,
                        size: isSmallScreen ? 36 : 44,
                        color: Colors.indigo[700],
                      ),
                      SizedBox(height: isSmallScreen ? 12 : 16),
                      Text(
                        'Join the Community',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 20 : 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo[900],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: isSmallScreen ? 8 : 12),
                      Text(
                        'Enter your JWT token to access',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 14 : 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: isSmallScreen ? 16 : 24),
                      TextField(
                        controller: controller,
                        decoration: InputDecoration(
                          hintText: 'JWT Token',
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide(color: Colors.indigo[700]!, width: 2),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: isSmallScreen ? 12 : 16,
                          ),
                        ),
                        style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                      ),
                      SizedBox(height: isSmallScreen ? 16 : 24),
                      ElevatedButton(
                        onPressed: () => provider.login(controller.text),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.indigo[700],
                          foregroundColor: Colors.white,
                          minimumSize: Size(double.infinity, isSmallScreen ? 44 : 50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          elevation: 3,
                        ),
                        child: Text(
                          'Login',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 14 : 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Header Widget
class HeaderWidget extends StatelessWidget implements PreferredSizeWidget {
  final User user;

  const HeaderWidget({Key? key, required this.user}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    
    // Determine title based on user role
    String title = 'Student Hub';
    if (user.role == 'mendor' || user.type == 'mendor') {
      title = 'Mentor Community';
    }

    return Container(
      color: Colors.indigo[700],
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isSmallScreen ? 12 : 16,
            vertical: isSmallScreen ? 8 : 12,
          ),
          child: Row(
            children: [
              IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                  size: isSmallScreen ? 20 : 24,
                ),
                onPressed: () => Navigator.pop(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
              SizedBox(width: isSmallScreen ? 8 : 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 18 : 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                    ),
                    builder: (context) => Container(
                      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircleAvatar(
                            radius: isSmallScreen ? 30 : 36,
                            backgroundColor: Colors.indigo[500],
                            child: Text(
                              user.username[0].toUpperCase(),
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: isSmallScreen ? 20 : 24,
                              ),
                            ),
                          ),
                          SizedBox(height: isSmallScreen ? 8 : 12),
                          Text(
                            user.username,
                            style: TextStyle(
                              fontSize: isSmallScreen ? 16 : 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.indigo[900],
                            ),
                          ),
                          Text(
                            user.type ?? (user.role == 'mendor' ? 'Mentor' : 'Student'),
                            style: TextStyle(
                              fontSize: isSmallScreen ? 12 : 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: isSmallScreen ? 12 : 16),
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(
                              'Close',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 14 : 16,
                                color: Colors.indigo[700],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                child: CircleAvatar(
                  radius: isSmallScreen ? 16 : 20,
                  backgroundColor: Colors.indigo[500],
                  child: Text(
                    user.username[0].toUpperCase(),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}

// Tab Section
class TabSection extends StatelessWidget {
  final CommunityProvider provider;
  final TabController tabController;

  const TabSection({Key? key, required this.provider, required this.tabController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 8 : 12),
      child: TabBar(
        controller: tabController,
        labelColor: Colors.indigo[700],
        unselectedLabelColor: Colors.grey[500],
        indicatorColor: Colors.indigo[700],
        indicatorPadding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 24),
        labelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: isSmallScreen ? 14 : 16,
        ),
        tabs: [
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 4 : 8),
                Text('Discussions'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.photo_library_outlined,
                  size: isSmallScreen ? 18 : 20,
                ),
                SizedBox(width: isSmallScreen ? 4 : 8),
                Text('Gallery'),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
