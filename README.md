# sasthra_mobile_app

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.




# Mentor Dashboard Module

## Overview
The Mentor Dashboard is a comprehensive module that allows mentors to manage their online status, receive incoming calls from students, and conduct video sessions using LiveKit integration.

## Features

### 1. **Automatic Status Management**
- When a mentor logs in, their status is automatically set to "online"
- Mentors can manually change their status between:
  - **Online**: Available to receive calls
  - **Away**: Temporarily unavailable
  - **Offline**: Not available for calls

### 2. **Call Polling System**
- When online, the system polls `/mentor/poll?mentor_id=${mentorId}` every 5 seconds
- Detects incoming calls from students
- Displays incoming call notification with accept/decline options
- Plays ringtone when calls are received

### 3. **Video Call Integration**
- Uses LiveKit for real-time video/audio communication
- Automatic camera and microphone activation upon call acceptance
- Video controls: mute/unmute, camera on/off, screen sharing
- Call duration tracking
- Proper cleanup when calls end

### 4. **API Integration**
- **Status Management**: `POST /mentor-session` with action (online/away/offline)
- **Call Polling**: `GET /mentor/poll?mentor_id=${mentorId}`
- **Accept Call**: `POST /call/accept` with mentor_id and room_name
- **End Call**: `POST /call/end` with room_name

## File Structure

```
lib/features/mendor/mentor_dashboard/
├── mentor_dashboard.dart      # Main dashboard component
├── incoming_call.dart         # Video call interface
└── README.md                 # This documentation
```

## Key Components

### MentorDashboard
- Main dashboard interface
- Status management UI
- Call polling logic
- Incoming call notifications
- Navigation to video call interface

### IncomingCall
- LiveKit video call interface
- Video/audio controls
- Call duration tracking
- Screen sharing functionality
- Call termination handling

## Usage

### Basic Integration
```dart
import 'package:sasthra/features/mendor/mentor_dashboard/mentor_dashboard.dart';

// In your app
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const MentorDashboard()),
);
```

### Demo Usage
```dart
import 'package:sasthra/demo/mentor_dashboard_demo.dart';

void main() {
  runApp(const MentorDashboardDemo());
}
```

## Dependencies

### Required Packages
- `livekit_client: ^2.1.0` - Video/audio communication
- `audioplayers: ^5.2.1` - Ringtone playback
- `http: ^0.13.5` - API communication
- `flutter/material.dart` - UI components

### Core Services
- `StorageService` - User data persistence
- `TokenService` - Authentication token management
- `AppLogger` - Logging functionality

## API Endpoints

### 1. Mentor Session Management
```http
POST /mentor-session
Content-Type: application/json
Authorization: Bearer {token}

{
  "action": "online|away|offline",
  "mentor_id": "string",
  "session_id": "string"
}
```

### 2. Call Polling
```http
GET /mentor/poll?mentor_id={mentorId}
Authorization: Bearer {token}

Response:
{
  "incoming_call": boolean,
  "room_name": "string",
  "student_id": "string",
  "timestamp": "string"
}
```

### 3. Accept Call
```http
POST /call/accept
Content-Type: application/json
Authorization: Bearer {token}

{
  "mentor_id": "string",
  "room_name": "string"
}

Response:
{
  "token": "livekit_token"
}
```

### 4. End Call
```http
POST /call/end
Content-Type: application/json
Authorization: Bearer {token}

{
  "room_name": "string",
  "mentor_id": "string"
}
```

## Configuration

### Environment Setup
- Base URL: Configured via `AppConfig.baseUrl`
- LiveKit URL: `wss://livekit.sasthra.in`
- Polling interval: 5 seconds
- Ringtone: `assets/audio/phone-ringtone-cabinet-356927.mp3`

### Required Permissions
- Camera access for video calls
- Microphone access for audio
- Network access for API calls

## Error Handling

### Common Error Scenarios
1. **No user data**: Redirects to login
2. **Network errors**: Displays error messages with retry options
3. **LiveKit connection failures**: Shows connection error with retry
4. **API failures**: Logs errors and shows user-friendly messages

### Logging
All actions are logged using `AppLogger` for debugging and monitoring:
- Status changes
- Call events
- API responses
- Error conditions

## Testing

### Demo Application
Run the demo to test functionality:
```bash
flutter run lib/demo/mentor_dashboard_demo.dart
```

### Manual Testing Checklist
- [ ] Login and automatic online status
- [ ] Manual status changes (online/away/offline)
- [ ] Call polling when online
- [ ] Incoming call notifications
- [ ] Call acceptance and video interface
- [ ] Video/audio controls
- [ ] Call termination
- [ ] Error handling scenarios

## Troubleshooting

### Common Issues
1. **Storage not initialized**: Ensure `StorageService.init()` is called
2. **Token missing**: Verify user is properly authenticated
3. **LiveKit connection fails**: Check network and LiveKit server status
4. **Ringtone not playing**: Verify audio file exists in assets

### Debug Mode
Enable debug logging by setting `AppConfig.isDebug = true`
