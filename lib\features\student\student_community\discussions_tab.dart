import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../../../core/config/app_config.dart';
import 'student_community_page.dart';

class DiscussionsTab extends StatefulWidget {
  final CommunityProvider provider;

  const DiscussionsTab({Key? key, required this.provider}) : super(key: key);

  @override
  _DiscussionsTabState createState() => _DiscussionsTabState();
}

class _DiscussionsTabState extends State<DiscussionsTab> with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _replyController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.elasticOut),
    );
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _replyController.dispose();
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _handleReply() async {
    if (_replyController.text.trim().isEmpty || widget.provider.selectedThread == null) return;
    
    final content = _replyController.text.trim();
    final threadId = widget.provider.selectedThread!.id;
    
    print('Handling reply: $content to thread: $threadId');
    
    try {
      await widget.provider.addReply(content, threadId);
      _replyController.clear();
      widget.provider.clearNewReply();
      
      // Scroll to bottom after adding reply
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not send reply: $e'))
      );
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 360;
    final isMediumScreen = screenWidth < 600;

    return Consumer<CommunityProvider>(
      builder: (context, provider, _) {
        if (provider.loadingThreads) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.indigo.shade50,
                  Colors.blue.shade50,
                  Colors.purple.shade50,
                ],
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitWave(
                    color: Colors.indigo,
                    size: isSmallScreen ? 40.0 : 50.0,
                  ),
                  SizedBox(height: isSmallScreen ? 12 : 16),
                  Text(
                    'Loading discussions...',
                    style: TextStyle(
                      color: Colors.indigo,
                      fontSize: isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.grey.shade50,
                Colors.blue.shade500,
                Colors.indigo.shade500,
              ],
            ),
          ),
          child: Column(
            children: [
              // Thread List (Top Section)
              Expanded(
                flex: 2,
                child: Container(
                  margin: EdgeInsets.all(isSmallScreen ? 4 : 8),
                  padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.indigo.withOpacity(0.1),
                        blurRadius: isSmallScreen ? 10 : 20,
                        offset: Offset(0, isSmallScreen ? 4 : 8),
                      ),
                      BoxShadow(
                        color: Colors.white.withOpacity(0.8),
                        blurRadius: isSmallScreen ? 10 : 20,
                        offset: Offset(0, isSmallScreen ? -4 : -8),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      // Create New Thread
                      Container(
                        padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.indigo.shade100,
                              Colors.blue.shade100,
                              Colors.purple.shade50,
                            ],
                          ),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(isSmallScreen ? 16 : 24),
                            topRight: Radius.circular(isSmallScreen ? 16 : 24),
                          ),
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.indigo.shade100,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                                  decoration: BoxDecoration(
                                    color: Colors.indigo.shade600,
                                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                                  ),
                                  child: Icon(
                                    Icons.forum_rounded,
                                    color: Colors.white,
                                    size: isSmallScreen ? 16 : 20,
                                  ),
                                ),
                                SizedBox(width: isSmallScreen ? 8 : 12),
                                Text(
                                  'Discussions',
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 16 : 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.indigo.shade700,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: isSmallScreen ? 12 : 16),
                            Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.indigo.withOpacity(0.1),
                                          blurRadius: isSmallScreen ? 4 : 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: TextField(
                                      controller: _messageController,
                                      onChanged: provider.setNewMessage,
                                      decoration: InputDecoration(
                                        hintText: '💭 Start a new discussion...',
                                        hintStyle: TextStyle(
                                          color: Colors.grey.shade500,
                                          fontSize: isSmallScreen ? 12 : 14,
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                          borderSide: BorderSide.none,
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                          borderSide: BorderSide(
                                            color: Colors.indigo.shade400,
                                            width: 2,
                                          ),
                                        ),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: isSmallScreen ? 12 : 16,
                                          vertical: isSmallScreen ? 10 : 14,
                                        ),
                                        filled: true,
                                        fillColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: isSmallScreen ? 8 : 12),
                                ScaleTransition(
                                  scale: _fabAnimation,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: provider.newMessage.isNotEmpty
                                            ? [Colors.indigo.shade600, Colors.indigo.shade800]
                                            : [Colors.grey.shade300, Colors.grey.shade400],
                                      ),
                                      borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                      boxShadow: provider.newMessage.isNotEmpty
                                          ? [
                                              BoxShadow(
                                                color: Colors.indigo.withOpacity(0.3),
                                                blurRadius: isSmallScreen ? 4 : 8,
                                                offset: const Offset(0, 4),
                                              ),
                                            ]
                                          : [],
                                    ),
                                    child: IconButton(
                                      onPressed: provider.newMessage.isNotEmpty
                                          ? () {
                                              final content = provider.newMessage;
                                              provider.createThread(content);
                                              _messageController.clear();
                                              provider.setNewMessage('');
                                            }
                                          : null,
                                      icon: const Icon(Icons.send_rounded),
                                      color: Colors.white,
                                      iconSize: isSmallScreen ? 16 : 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // Thread List
                      Expanded(
                        child: ListView.builder(
                          padding: EdgeInsets.all(isSmallScreen ? 8 : 16),
                          itemCount: provider.localThreads.length,
                          itemBuilder: (context, index) {
                            final thread = provider.localThreads[index];
                            final isSelected = provider.selectedThread?.id == thread.id;
                            
                            return GestureDetector(
                              onTap: () => provider.setSelectedThread(thread),
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 400),
                                curve: Curves.easeInOut,
                                margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
                                padding: EdgeInsets.all(isSmallScreen ? 12 : 18),
                                decoration: BoxDecoration(
                                  gradient: isSelected
                                      ? LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.indigo.shade50,
                                            Colors.blue.shade50,
                                            Colors.purple.shade100,
                                          ],
                                        )
                                      : LinearGradient(
                                          colors: [Colors.white, Colors.white],
                                        ),
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                                  border: Border.all(
                                    color: isSelected 
                                        ? Colors.indigo.shade300 
                                        : Colors.grey.shade200,
                                    width: isSelected ? 2 : 1,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: isSelected 
                                          ? Colors.indigo.withOpacity(0.2)
                                          : Colors.grey.withOpacity(0.1),
                                      blurRadius: isSelected ? 8 : 6,
                                      offset: Offset(0, isSelected ? 4 : 3),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      thread.content,
                                      style: TextStyle(
                                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                        color: isSelected ? Colors.indigo.shade800 : Colors.grey.shade800,
                                        fontSize: isSmallScreen ? 14 : 15,
                                        height: 1.4,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: isSmallScreen ? 8 : 12),
                                    Row(
                                      children: [
                                        Container(
                                          width: isSmallScreen ? 8 : 10,
                                          height: isSmallScreen ? 8 : 10,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: [Colors.green.shade400, Colors.green.shade600],
                                            ),
                                            shape: BoxShape.circle,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.green.withOpacity(0.3),
                                                blurRadius: isSmallScreen ? 2 : 4,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: isSmallScreen ? 6 : 10),
                                        Expanded(
                                          child: Text(
                                            '${thread.firstName} • ${thread.senderType}',
                                            style: TextStyle(
                                              fontSize: isSmallScreen ? 12 : 13,
                                              color: Colors.grey.shade600,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          provider.formatRelativeTime(thread.createdAt),
                                          style: TextStyle(
                                            fontSize: isSmallScreen ? 10 : 11,
                                            color: Colors.grey.shade500,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                    if (thread.subthreads.isNotEmpty)
                                      Container(
                                        margin: EdgeInsets.only(top: isSmallScreen ? 6 : 10),
                                        padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 6 : 10, vertical: isSmallScreen ? 4 : 6),
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              Colors.indigo.shade100,
                                              Colors.blue.shade100,
                                            ],
                                          ),
                                          borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                                          border: Border.all(
                                            color: Colors.indigo.shade200,
                                            width: 1,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.chat_bubble_outline_rounded,
                                              size: isSmallScreen ? 10 : 12,
                                              color: Colors.indigo.shade600,
                                            ),
                                            SizedBox(width: isSmallScreen ? 2 : 4),
                                            Text(
                                              '${thread.subthreads.length} replies',
                                              style: TextStyle(
                                                fontSize: isSmallScreen ? 10 : 11,
                                                color: Colors.indigo.shade700,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Conversation View (Bottom Section)
              Expanded(
                flex: 3,
                child: provider.selectedThread == null
                    ? Container(
                        margin: EdgeInsets.all(isSmallScreen ? 4 : 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.grey.shade50,
                              Colors.grey.shade100,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                          border: Border.all(
                            color: Colors.grey.shade200,
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TweenAnimationBuilder<double>(
                                tween: Tween(begin: 0.0, end: 1.0),
                                duration: const Duration(milliseconds: 800),
                                builder: (context, value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: Container(
                                      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.indigo.shade100,
                                            Colors.blue.shade100,
                                          ],
                                        ),
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.indigo.withOpacity(0.2),
                                            blurRadius: isSmallScreen ? 10 : 20,
                                            offset: const Offset(0, 8),
                                          ),
                                        ],
                                      ),
                                      child: Icon(
                                        Icons.chat_bubble_outline_rounded,
                                        size: isSmallScreen ? 36 : 48,
                                        color: Colors.indigo.shade600,
                                      ),
                                    ),
                                  );
                                },
                              ),
                              SizedBox(height: isSmallScreen ? 16 : 24),
                              Text(
                                'Select a thread to start conversation',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 16 : 18,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: isSmallScreen ? 6 : 8),
                              Text(
                                'Choose from the discussions above',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 12 : 14,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : _buildConversationView(provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConversationView(CommunityProvider provider) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 4 : 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
        boxShadow: [
          BoxShadow(
            color: Colors.indigo.withOpacity(0.1),
            blurRadius: isSmallScreen ? 10 : 20,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Thread Header
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.indigo.shade50,
                  Colors.blue.shade50,
                  Colors.purple.shade500,
                ],
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(isSmallScreen ? 16 : 24),
                topRight: Radius.circular(isSmallScreen ? 16 : 24),
              ),
              border: Border(
                bottom: BorderSide(color: Colors.indigo.shade100),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 6 : 8),
                  decoration: BoxDecoration(
                    color: Colors.indigo.shade600,
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  ),
                  child: Icon(
                    Icons.forum_rounded,
                    color: Colors.white,
                    size: isSmallScreen ? 12 : 16,
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider.selectedThread!.content,
                        style: TextStyle(
                          fontSize: isSmallScreen ? 16 : 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo.shade800,
                          height: 1.3,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 4 : 6),
                      Row(
                        children: [
                          Container(
                            width: isSmallScreen ? 6 : 8,
                            height: isSmallScreen ? 6 : 8,
                            decoration: BoxDecoration(
                              color: Colors.green.shade500,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: isSmallScreen ? 6 : 8),
                          Text(
                            '${provider.selectedThread!.firstName} • ${provider.selectedThread!.senderType} • ${provider.formatRelativeTime(provider.selectedThread!.createdAt)}',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 11 : 13,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
                  ),
                  child: IconButton(
                    onPressed: () => provider.setSelectedThread(null),
                    icon: Icon(
                      Icons.close_rounded,
                      color: Colors.grey.shade600,
                      size: isSmallScreen ? 16 : 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Replies/Messages
          Expanded(
            child: provider.selectedThread!.subthreads.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.blue.shade50,
                                Colors.indigo.shade50,
                              ],
                            ),
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '💬',
                            style: TextStyle(fontSize: isSmallScreen ? 24 : 32),
                          ),
                        ),
                        SizedBox(height: isSmallScreen ? 12 : 16),
                        Text(
                          'Be the first to reply!',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: isSmallScreen ? 16 : 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: isSmallScreen ? 6 : 8),
                        Text(
                          'Start the conversation below',
                          style: TextStyle(
                            color: Colors.grey.shade500,
                            fontSize: isSmallScreen ? 12 : 14,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
                    itemCount: provider.selectedThread!.subthreads.length,
                    itemBuilder: (context, index) {
                      final reply = provider.selectedThread!.subthreads[index];
                      final isMyReply = reply.senderId == provider.user!.id;
                      
                      return Container(
                        margin: EdgeInsets.only(
                          left: isMyReply ? 30 : 8,
                          right: isMyReply ? 8 : 30,
                          bottom: isSmallScreen ? 8 : 16,
                        ),
                        child: Row(
                          mainAxisAlignment: isMyReply 
                              ? MainAxisAlignment.end 
                              : MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!isMyReply) ...[
                              Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.indigo.shade300,
                                      Colors.indigo.shade500,
                                    ],
                                  ),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.indigo.withOpacity(0.3),
                                      blurRadius: isSmallScreen ? 4 : 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: CircleAvatar(
                                  radius: isSmallScreen ? 14 : 18,
                                  backgroundColor: Colors.transparent,
                                  child: Text(
                                    reply.firstName[0].toUpperCase(),
                                    style: TextStyle(
                                      fontSize: isSmallScreen ? 12 : 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: isSmallScreen ? 8 : 12),
                            ],
                            Flexible(
                              child: Container(
                                padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                                decoration: BoxDecoration(
                                  gradient: isMyReply 
                                      ? LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.indigo.shade500,
                                            Colors.indigo.shade700,
                                          ],
                                        )
                                      : LinearGradient(
                                          colors: [
                                            Colors.grey.shade50,
                                            Colors.grey.shade100,
                                          ],
                                        ),
                                  borderRadius: BorderRadius.only(
                                    topLeft: const Radius.circular(16),
                                    topRight: const Radius.circular(16),
                                    bottomLeft: Radius.circular(isMyReply ? 16 : 4),
                                    bottomRight: Radius.circular(isMyReply ? 4 : 16),
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: isMyReply 
                                          ? Colors.indigo.withOpacity(0.3)
                                          : Colors.grey.withOpacity(0.2),
                                      blurRadius: isSmallScreen ? 4 : 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (!isMyReply)
                                      Padding(
                                        padding: EdgeInsets.only(bottom: isSmallScreen ? 4 : 6),
                                        child: Text(
                                          '${reply.firstName} • ${reply.senderType}',
                                          style: TextStyle(
                                            fontSize: isSmallScreen ? 10 : 11,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                      ),
                                    Text(
                                      reply.content,
                                      style: TextStyle(
                                        color: isMyReply 
                                            ? Colors.white 
                                            : Colors.grey.shade800,
                                        fontSize: isSmallScreen ? 14 : 15,
                                        height: 1.4,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    SizedBox(height: isSmallScreen ? 4 : 6),
                                    Text(
                                      provider.formatRelativeTime(reply.createdAt),
                                      style: TextStyle(
                                        fontSize: isSmallScreen ? 10 : 11,
                                        color: isMyReply 
                                            ? Colors.white.withOpacity(0.8)
                                            : Colors.grey.shade500,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            if (isMyReply) ...[
                              SizedBox(width: isSmallScreen ? 8 : 12),
                              Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.indigo.shade600,
                                      Colors.indigo.shade800,
                                    ],
                                  ),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.indigo.withOpacity(0.4),
                                      blurRadius: isSmallScreen ? 4 : 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: CircleAvatar(
                                  radius: isSmallScreen ? 14 : 18,
                                  backgroundColor: Colors.transparent,
                                  child: Text(
                                    reply.firstName[0].toUpperCase(),
                                    style: TextStyle(
                                      fontSize: isSmallScreen ? 12 : 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      );
                    },
                  ),
          ),
          // Reply Input
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 20),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(color: Colors.grey.shade200),
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(isSmallScreen ? 16 : 24),
                bottomRight: Radius.circular(isSmallScreen ? 16 : 24),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      controller: _replyController,
                      onChanged: provider.setNewReply,
                      decoration: InputDecoration(
                        hintText: '💭 Type your reply...',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 24),
                          borderSide: BorderSide(
                            color: Colors.indigo.shade400,
                            width: 2,
                          ),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 12 : 20,
                          vertical: isSmallScreen ? 10 : 14,
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      maxLines: null,
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 8 : 12),
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: provider.newReply.isNotEmpty
                          ? [Colors.indigo.shade500, Colors.indigo.shade700]
                          : [Colors.grey.shade300, Colors.grey.shade400],
                    ),
                    borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
                    boxShadow: provider.newReply.isNotEmpty
                        ? [
                            BoxShadow(
                              color: Colors.indigo.withOpacity(0.4),
                              blurRadius: isSmallScreen ? 4 : 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : [],
                  ),
                  child: IconButton(
                    onPressed: provider.newReply.isNotEmpty ? _handleReply : null,
                    icon: const Icon(Icons.send_rounded),
                    color: Colors.white,
                    iconSize: isSmallScreen ? 16 : 20,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}