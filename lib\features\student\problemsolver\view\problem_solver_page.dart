import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import '../../../../core/config/app_config.dart';
import '../controller/problem_solver_controller.dart';
import '../model/search_result_models.dart';
import 'package:flutter_math_fork/flutter_math.dart';

class ProblemSolverPage extends StatefulWidget {
  const ProblemSolverPage({super.key});

  @override
  State<ProblemSolverPage> createState() => _ProblemSolverPageState();
}

class _ProblemSolverPageState extends State<ProblemSolverPage>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _animationController, curve: Curves.easeInOutCubic),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOutCubic,
        );
      }
    });
  }

  void _showSnackbar(String title, String message, ContentType type) {
    final snackBar = SnackBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      behavior: SnackBarBehavior.floating,
      content: AwesomeSnackbarContent(
        title: title,
        message: message,
        contentType: type,
      ),
      duration: const Duration(seconds: 3),
      padding: const EdgeInsets.symmetric(vertical: 10),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final isTablet = screenWidth >= 600;
    final isLargeScreen = screenWidth >= 1200;

    // Dynamic font scaling with upper and lower bounds
    final baseFontSize = (isLargeScreen
            ? screenWidth * 0.025
            : isTablet
                ? screenWidth * 0.03
                : screenWidth * 0.035)
        .clamp(14.0, 20.0); // Prevent font from being too small or too large

    return ChangeNotifierProvider(
      create: (_) => ProblemSolverController(),
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        appBar: _buildModernAppBar(context, screenWidth, baseFontSize),
        body: SafeArea(
          child: Consumer<ProblemSolverController>(
            builder: (context, controller, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    _buildEnhancedSettingsPanel(
                        controller, screenWidth, screenHeight, isTablet, isLargeScreen, baseFontSize),
                    Expanded(
                      child: _buildMainContent(
                          controller, screenWidth, screenHeight, isTablet, isLargeScreen, baseFontSize),
                    ),
                    _buildEnhancedInputPanel(controller, screenWidth, isTablet, baseFontSize),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar(BuildContext context, double screenWidth, double baseFontSize) {
    return PreferredSize(
      preferredSize: Size.fromHeight(screenWidth * 0.18),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
        child: AppBar(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  'Sparkit',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: baseFontSize * 1.2,
                    letterSpacing: 0.5,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: screenWidth * 0.02),
              Flexible(
                child: SizedBox(
                  width: screenWidth * 0.3,
                  height: screenWidth * 0.15,
                  child: Image.asset(
                    'assets/botTwo.gif',
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: const Color(0xFF3B82F6),
          foregroundColor: Colors.white,
          elevation: 2,
          centerTitle: true,
          titleSpacing: 0,
        ),
      ),
    );
  }

  Widget _buildEnhancedSettingsPanel(
      ProblemSolverController controller,
      double screenWidth,
      double screenHeight,
      bool isTablet,
      bool isLargeScreen,
      double baseFontSize) {
    return Container(
      margin: EdgeInsets.all(screenWidth * 0.03),
      padding: EdgeInsets.all(isTablet ? screenWidth * 0.05 : screenWidth * 0.04),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(screenWidth * 0.015),
                      decoration: BoxDecoration(
                        color: const Color(0xFF3B82F6).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(Icons.tune,
                          color: const Color(0xFF3B82F6), size: baseFontSize * 1.1),
                    ),
                    SizedBox(width: screenWidth * 0.02),
                    Text(
                      'Settings',
                      style: TextStyle(
                        fontSize: baseFontSize,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: screenWidth * 0.04),
                Row(
                  children: [
                    Container(
                      constraints: BoxConstraints(
                          maxWidth: isLargeScreen
                              ? screenWidth * 0.25
                              : isTablet
                                  ? screenWidth * 0.3
                                  : screenWidth * 0.35),
                      child: _buildModernDropdown(
                        controller,
                        'Mode',
                        controller.selectedMode,
                        controller.modes,
                        Icons.psychology_alt,
                        const Color(0xFF3B82F6),
                        screenWidth,
                        baseFontSize,
                      ),
                    ),
                    SizedBox(width: screenWidth * 0.03),
                    Container(
                      constraints: BoxConstraints(
                          maxWidth: isLargeScreen
                              ? screenWidth * 0.25
                              : isTablet
                                  ? screenWidth * 0.3
                                  : screenWidth * 0.35),
                      child: _buildModernDropdown(
                        controller,
                        'Language',
                        controller.selectedLanguage,
                        controller.languages,
                        Icons.language,
                        const Color(0xFF10B981),
                        screenWidth,
                        baseFontSize,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: screenWidth * 0.05),
                _buildModernActionButtons(controller, screenWidth, isTablet, baseFontSize),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernDropdown(
    ProblemSolverController controller,
    String type,
    String currentValue,
    List<String> items,
    IconData icon,
    Color color,
    double screenWidth,
    double baseFontSize,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withOpacity(0.3)),
        color: color.withOpacity(0.1),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        isDense: true,
        value: currentValue,
        decoration: InputDecoration(
          labelText: type,
          labelStyle: TextStyle(
            color: color,
            fontWeight: FontWeight.w600,
            fontSize: baseFontSize * 0.9,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(screenWidth * 0.015),
            padding: EdgeInsets.all(screenWidth * 0.015),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: color, size: baseFontSize * 1.1),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.02, vertical: screenWidth * 0.025),
        ),
        items: items.map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: SizedBox(
              width: screenWidth * 0.3,
              child: Text(
                value,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w600,
                  fontSize: baseFontSize * 0.9,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          if (type == 'Mode') {
            controller.setMode(newValue);
          } else {
            controller.setLanguage(newValue);
          }
        },
        dropdownColor: Colors.white,
        isExpanded: true,
        itemHeight: (baseFontSize * 2.8).clamp(48.0, double.infinity), // Ensure minimum height
        menuMaxHeight: screenWidth * 0.6,
      ),
    );
  }

  Widget _buildModernActionButtons(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            constraints: BoxConstraints(
                maxWidth: isTablet ? screenWidth * 0.3 : screenWidth * 0.25),
            child: _buildModernActionButton(
              'YouTube',
              Icons.play_circle_filled,
              const LinearGradient(colors: [Color(0xFFF43F5E), Color(0xFFDC2626)]),
              () => _handleSearch(controller, SearchType.youtubeSearch),
              controller.isLoading &&
                  controller.lastSearchType == SearchType.youtubeSearch,
              screenWidth,
              baseFontSize,
            ),
          ),
          SizedBox(width: isTablet ? screenWidth * 0.03 : screenWidth * 0.02),
          Container(
            constraints: BoxConstraints(
                maxWidth: isTablet ? screenWidth * 0.3 : screenWidth * 0.25),
            child: _buildModernActionButton(
              'Web',
              Icons.search,
              const LinearGradient(colors: [Color(0xFF10B981), Color(0xFF059669)]),
              () => _handleSearch(controller, SearchType.webSearch),
              controller.isLoading &&
                  controller.lastSearchType == SearchType.webSearch,
              screenWidth,
              baseFontSize,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernActionButton(
    String label,
    IconData icon,
    Gradient gradient,
    VoidCallback onPressed,
    bool isLoading,
    double screenWidth,
    double baseFontSize,
  ) {
    return Container(
      height: screenWidth * 0.1,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: gradient.colors.first.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading
            ? SizedBox(
                width: baseFontSize * 0.9,
                height: baseFontSize * 0.9,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              )
            : Icon(icon, size: baseFontSize),
        label: Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: baseFontSize * 0.9,
            overflow: TextOverflow.ellipsis,
          ),
          maxLines: 1,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.025, vertical: screenWidth * 0.015),
        ),
      ),
    );
  }

  Widget _buildMainContent(ProblemSolverController controller,
      double screenWidth, double screenHeight, bool isTablet, bool isLargeScreen, double baseFontSize) {
    if (controller.lastSearchType == SearchType.doubtSolver) {
      return _buildChatInterface(controller, screenWidth, isTablet, baseFontSize);
    } else if (controller.lastSearchType == SearchType.youtubeSearch) {
      return _buildYoutubeResults(controller, screenWidth, isTablet, baseFontSize);
    } else if (controller.lastSearchType == SearchType.webSearch) {
      return _buildWebResults(controller, screenWidth, isTablet, baseFontSize);
    } else {
      return _buildWelcomeScreen(screenWidth, screenHeight, isTablet, baseFontSize);
    }
  }

  Widget _buildWelcomeScreen(
      double screenWidth, double screenHeight, bool isTablet, double baseFontSize) {
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(isTablet ? screenWidth * 0.06 : screenWidth * 0.05),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF3B82F6), Color(0xFF8B5CF6)],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF3B82F6).withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Icon(Icons.psychology_alt,
                  size: isTablet ? screenWidth * 0.15 : screenWidth * 0.12,
                  color: Colors.white),
            ),
            SizedBox(height: screenHeight * 0.025),
            Text(
              'Welcome to Sparkit',
              style: TextStyle(
                fontSize: isTablet ? baseFontSize * 1.8 : baseFontSize * 1.6,
                fontWeight: FontWeight.w800,
                color: const Color(0xFF1F2937),
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
            SizedBox(height: screenHeight * 0.01),
            Text(
              'Ask anything, get instant solutions',
              style: TextStyle(
                fontSize: baseFontSize * 0.9,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
            SizedBox(height: screenHeight * 0.03),
            Container(
              padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.04, vertical: screenHeight * 0.01),
              decoration: BoxDecoration(
                color: const Color(0xFF3B82F6).withOpacity(0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '✨ Powered by AI',
                style: TextStyle(
                  color: const Color(0xFF3B82F6),
                  fontWeight: FontWeight.w600,
                  fontSize: baseFontSize * 0.9,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatInterface(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.03),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(isTablet ? screenWidth * 0.05 : screenWidth * 0.04),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF6B7280), Color(0xFF3B82F6)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(screenWidth * 0.015),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.psychology_alt,
                      color: Colors.white, size: baseFontSize * 1.1),
                ),
                SizedBox(width: screenWidth * 0.02),
                Text(
                  'Sparkit',
                  style: TextStyle(
                    fontSize: baseFontSize,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: screenWidth * 0.025, vertical: screenWidth * 0.01),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.25),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${controller.selectedMode} • ${controller.selectedLanguage}',
                    style: TextStyle(
                      fontSize: baseFontSize * 0.75,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _buildChatMessages(controller, screenWidth, isTablet, baseFontSize),
          ),
        ],
      ),
    );
  }

  Widget _buildChatMessages(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    List<Widget> messages = [];
    if (controller.doubtText.isNotEmpty ||
        controller.imageFile != null ||
        controller.audioPath != null) {
      messages.add(_buildUserMessage(controller, screenWidth, isTablet, baseFontSize));
    }
    if (controller.response.isNotEmpty) {
      messages.add(_buildAIMessage(controller.response, screenWidth, isTablet, baseFontSize));
    }
    if (controller.history != null && controller.history!.isNotEmpty) {
      for (var msg in controller.history!) {
        if (msg['role'] == 'user') {
          messages.add(
              _buildHistoryUserMessage(msg['content'], screenWidth, isTablet, baseFontSize));
        } else if (msg['role'] == 'assistant') {
          messages.add(
              _buildHistoryAIMessage(msg['content'], screenWidth, isTablet, baseFontSize));
        }
      }
    }
    if (controller.error.isNotEmpty) {
      messages.add(_buildErrorMessage(controller.error, screenWidth, baseFontSize));
    }
    if (messages.isEmpty) {
      return Center(
        child: Text(
          'Start a conversation with Sparkit',
          style: TextStyle(
              color: Colors.grey[600],
              fontSize: baseFontSize * 0.9,
              fontWeight: FontWeight.w500),
        ),
      );
    }
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(screenWidth * 0.03),
      itemCount: messages.length,
      itemBuilder: (context, index) => messages[index],
    );
  }

  Widget _buildUserMessage(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        constraints: BoxConstraints(
            maxWidth: isTablet ? screenWidth * 0.75 : screenWidth * 0.7),
        margin: EdgeInsets.only(
            bottom: screenWidth * 0.03,
            left: isTablet ? screenWidth * 0.12 : screenWidth * 0.1),
        padding: EdgeInsets.all(screenWidth * 0.03),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF3B82F6), Color(0xFF6B7280)],
          ),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(4),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF3B82F6).withOpacity(0.3),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (controller.doubtText.isNotEmpty)
              Text(
                controller.doubtText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: baseFontSize * 0.9,
                  fontWeight: FontWeight.w500,
                  height: 1.4,
                ),
                softWrap: true,
              ),
            if (controller.imageFile != null) ...[
              SizedBox(height: screenWidth * 0.02),
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.file(
                  controller.imageFile!,
                  height: isTablet ? screenWidth * 0.25 : screenWidth * 0.2,
                  width: isTablet ? screenWidth * 0.45 : screenWidth * 0.35,
                  fit: BoxFit.cover,
                ),
              ),
            ],
            if (controller.audioPath != null) ...[
              SizedBox(height: screenWidth * 0.02),
              Container(
                padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.025, vertical: screenWidth * 0.015),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.25),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.audiotrack,
                        color: Colors.white, size: baseFontSize),
                    SizedBox(width: screenWidth * 0.015),
                    Text(
                      'Audio message',
                      style: TextStyle(color: Colors.white, fontSize: baseFontSize * 0.9),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _preprocessResponse(String response) {
    String cleaned = response
        .replaceAll(RegExp(r'\$\s+\$'), r'$$')
        .replaceAll(RegExp(r'\r\n|\r'), '\n')
        .replaceAllMapped(RegExp(r'(\${1,2})([\s\S]*?)\1'), (match) {
      return match.group(0)!.replaceAll('\n', ' ');
    }).trim();
    return cleaned;
  }

  Widget _buildAIMessage(String message, double screenWidth, bool isTablet, double baseFontSize) {
    final processedText = _preprocessResponse(message);
    if (processedText.isEmpty) {
      return Text(
        'Received an empty response.',
        style: TextStyle(
            color: Colors.grey[600],
            fontSize: baseFontSize * 0.9,
            fontWeight: FontWeight.w500),
      );
    }
    final paragraphs = processedText.split(RegExp(r'\n\s*\n+'));
    List<Widget> contentWidgets = [];
    for (final paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) continue;
      contentWidgets.add(_buildParagraphWidget(paragraph.trim(), screenWidth, baseFontSize));
    }
    if (contentWidgets.isEmpty) {
      return Text(
        'No content to display',
        style: TextStyle(
            color: Colors.grey[600],
            fontSize: baseFontSize * 0.9,
            fontWeight: FontWeight.w500),
      );
    }
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        constraints: BoxConstraints(
            maxWidth: isTablet ? screenWidth * 0.75 : screenWidth * 0.7),
        margin: EdgeInsets.only(
            bottom: screenWidth * 0.03,
            right: isTablet ? screenWidth * 0.12 : screenWidth * 0.1),
        padding: EdgeInsets.all(screenWidth * 0.03),
        decoration: BoxDecoration(
          color: const Color(0xFFF9FAFB),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
            bottomLeft: Radius.circular(4),
            bottomRight: Radius.circular(12),
          ),
          border: Border.all(color: const Color(0xFFE5E7EB), width: 1.2),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(screenWidth * 0.01),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF3B82F6), Color(0xFF6B7280)],
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(Icons.psychology_alt,
                      size: baseFontSize * 0.9, color: Colors.white),
                ),
                SizedBox(width: screenWidth * 0.015),
                Text(
                  'Sparkit',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF3B82F6),
                    fontSize: baseFontSize * 0.9,
                  ),
                ),
              ],
            ),
            SizedBox(height: screenWidth * 0.02),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: contentWidgets,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParagraphWidget(String paragraph, double screenWidth, double baseFontSize) {
    List<Widget> inlineWidgets = [];
    final RegExp latexRegex = RegExp(r'(\$\$.*?\$\$)|(\$.*?\$)');
    int lastMatchEnd = 0;

    for (final match in latexRegex.allMatches(paragraph)) {
      if (match.start > lastMatchEnd) {
        inlineWidgets.add(Text(
          paragraph.substring(lastMatchEnd, match.start),
          style: TextStyle(
              color: const Color(0xFF1F2937),
              fontSize: baseFontSize * 0.9,
              height: 1.4,
              fontWeight: FontWeight.w500),
          softWrap: true,
        ));
      }
      final latexText = match.group(0)!;
      final bool isDisplayMode = latexText.startsWith(r'$$');
      final String cleanedLatex = latexText
          .substring(isDisplayMode ? 2 : 1, latexText.length - (isDisplayMode ? 2 : 1))
          .trim();
      if (cleanedLatex.isNotEmpty) {
        inlineWidgets.add(
          Math.tex(
            cleanedLatex,
            mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
            textStyle: TextStyle(fontSize: baseFontSize * 0.9, fontWeight: FontWeight.w500),
            onErrorFallback: (err) {
              print("LaTeX Rendering Error: ${err.message} for \"$cleanedLatex\"");
              return Text(
                '[$cleanedLatex]',
                style: TextStyle(
                    color: Colors.red[600],
                    fontWeight: FontWeight.bold,
                    fontSize: baseFontSize * 0.9),
              );
            },
          ),
        );
      }
      lastMatchEnd = match.end;
    }
    if (lastMatchEnd < paragraph.length) {
      inlineWidgets.add(Text(
        paragraph.substring(lastMatchEnd),
        style: TextStyle(
            color: const Color(0xFF1F2937),
            fontSize: baseFontSize * 0.9,
            height: 1.4,
            fontWeight: FontWeight.w500),
        softWrap: true,
      ));
    }
    return Padding(
      padding: EdgeInsets.symmetric(vertical: screenWidth * 0.01),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        runSpacing: screenWidth * 0.015,
        spacing: screenWidth * 0.01,
        children: inlineWidgets,
      ),
    );
  }

  Widget _buildHistoryUserMessage(
      String message, double screenWidth, bool isTablet, double baseFontSize) {
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        constraints: BoxConstraints(
            maxWidth: isTablet ? screenWidth * 0.75 : screenWidth * 0.7),
        margin: EdgeInsets.only(
            bottom: screenWidth * 0.015,
            left: isTablet ? screenWidth * 0.12 : screenWidth * 0.1),
        padding: EdgeInsets.all(screenWidth * 0.025),
        decoration: BoxDecoration(
          color: const Color(0xFF3B82F6).withOpacity(0.8),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF3B82F6).withOpacity(0.3),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          message,
          style: TextStyle(
              color: Colors.white,
              fontSize: baseFontSize * 0.9,
              fontWeight: FontWeight.w500),
          softWrap: true,
        ),
      ),
    );
  }

  Widget _buildHistoryAIMessage(
      String message, double screenWidth, bool isTablet, double baseFontSize) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        constraints: BoxConstraints(
            maxWidth: isTablet ? screenWidth * 0.75 : screenWidth * 0.7),
        margin: EdgeInsets.only(
            bottom: screenWidth * 0.015,
            right: isTablet ? screenWidth * 0.12 : screenWidth * 0.1),
        padding: EdgeInsets.all(screenWidth * 0.025),
        decoration: BoxDecoration(
          color: const Color(0xFFE5E7EB),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          message,
          style: TextStyle(
              fontSize: baseFontSize * 0.9,
              height: 1.4,
              fontWeight: FontWeight.w500),
          softWrap: true,
        ),
      ),
    );
  }

  Widget _buildErrorMessage(String error, double screenWidth, double baseFontSize) {
    return Container(
      constraints: BoxConstraints(maxWidth: screenWidth * 0.85),
      margin: EdgeInsets.only(bottom: screenWidth * 0.03),
      padding: EdgeInsets.all(screenWidth * 0.03),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.red[200]!, width: 1.2),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600], size: baseFontSize),
          SizedBox(width: screenWidth * 0.015),
          Expanded(
            child: Text(
              error,
              style: TextStyle(
                  color: Colors.red[600],
                  fontSize: baseFontSize * 0.9,
                  fontWeight: FontWeight.w500),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYoutubeResults(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    if (controller.isLoading) {
      return Center(child: CircularProgressIndicator(color: const Color(0xFF3B82F6)));
    }
    if (controller.youtubeResults.isEmpty) {
      return _buildEmptyState(
          'No YouTube videos found', Icons.video_library, screenWidth, baseFontSize);
    }
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(screenWidth * 0.03),
            decoration: BoxDecoration(
              color: const Color(0xFFF43F5E).withOpacity(0.15),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.play_circle_filled,
                    color: const Color(0xFFF43F5E), size: baseFontSize),
                SizedBox(width: screenWidth * 0.015),
                Text(
                  'YouTube Results',
                  style: TextStyle(
                    fontSize: baseFontSize,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFFF43F5E),
                  ),
                ),
                const Spacer(),
                Text(
                  '${controller.youtubeResults.length} videos',
                  style: TextStyle(
                    fontSize: baseFontSize * 0.75,
                    color: const Color(0xFFF43F5E).withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(screenWidth * 0.03),
              itemCount: controller.youtubeResults.length,
              itemBuilder: (context, index) {
                final result = controller.youtubeResults[index];
                return _buildYoutubeCard(
                    result, controller, screenWidth, isTablet, baseFontSize);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYoutubeCard(YoutubeSearchResult result,
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return Card(
      elevation: 3,
      margin: EdgeInsets.only(bottom: screenWidth * 0.025),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: InkWell(
        onTap: () async {
          try {
            await controller
                .launchURL('https://www.youtube.com/watch?v=${result.videoId}');
            _showSnackbar('Success', 'Opening YouTube video', ContentType.success);
          } catch (e) {
            _showSnackbar(
                'Error', 'Failed to open YouTube video: $e', ContentType.failure);
          }
        },
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.025),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: result.thumbnailUrl.isNotEmpty
                    ? Image.network(
                        result.thumbnailUrl,
                        width: isTablet ? screenWidth * 0.25 : screenWidth * 0.2,
                        height: isTablet ? screenWidth * 0.15 : screenWidth * 0.12,
                        fit: BoxFit.cover,
                        errorBuilder: (c, o, s) => Container(
                          width: isTablet ? screenWidth * 0.25 : screenWidth * 0.2,
                          height: isTablet ? screenWidth * 0.15 : screenWidth * 0.12,
                          color: Colors.grey[300],
                          child: Icon(Icons.video_library,
                              size: baseFontSize * 2, color: Colors.grey[600]),
                        ),
                      )
                    : Container(
                        width: isTablet ? screenWidth * 0.25 : screenWidth * 0.2,
                        height: isTablet ? screenWidth * 0.15 : screenWidth * 0.12,
                        color: Colors.grey[300],
                        child: Icon(Icons.video_library,
                            size: baseFontSize * 2, color: Colors.grey[600]),
                      ),
              ),
              SizedBox(width: screenWidth * 0.025),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      result.title,
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: baseFontSize * 0.9,
                        color: const Color(0xFF1F2937),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenWidth * 0.01),
                    Text(
                      result.channelTitle,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: baseFontSize * 0.8,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenWidth * 0.015),
                    Row(
                      children: [
                        Icon(Icons.play_arrow,
                            size: baseFontSize * 0.9, color: const Color(0xFFF43F5E)),
                        SizedBox(width: screenWidth * 0.01),
                        Text(
                          'Watch on YouTube',
                          style: TextStyle(
                            color: const Color(0xFFF43F5E),
                            fontSize: baseFontSize * 0.8,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWebResults(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    if (controller.isLoading) {
      return Center(child: CircularProgressIndicator(color: const Color(0xFF10B981)));
    }
    if (controller.webResults.isEmpty) {
      return _buildEmptyState('No web results found', Icons.search, screenWidth, baseFontSize);
    }
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(screenWidth * 0.03),
            decoration: BoxDecoration(
              color: const Color(0xFF10B981).withOpacity(0.15),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.search,
                    color: const Color(0xFF10B981), size: baseFontSize),
                SizedBox(width: screenWidth * 0.015),
                Text(
                  'Web Results',
                  style: TextStyle(
                    fontSize: baseFontSize,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF10B981),
                  ),
                ),
                const Spacer(),
                Text(
                  '${controller.webResults.length} results',
                  style: TextStyle(
                    fontSize: baseFontSize * 0.75,
                    color: const Color(0xFF10B981).withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(screenWidth * 0.03),
              itemCount: controller.webResults.length,
              itemBuilder: (context, index) {
                final result = controller.webResults[index];
                return _buildWebCard(result, controller, screenWidth, isTablet, baseFontSize);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWebCard(WebSearchResult result,
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return Card(
      elevation: 3,
      margin: EdgeInsets.only(bottom: screenWidth * 0.025),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: InkWell(
        onTap: () async {
          try {
            await controller.launchURL(result.link);
            _showSnackbar('Success', 'Opening web link', ContentType.success);
          } catch (e) {
            _showSnackbar('Error', 'Failed to open web link: $e', ContentType.failure);
          }
        },
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.03),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                result.title,
                style: TextStyle(
                  color: const Color(0xFF1D4ED8),
                  fontWeight: FontWeight.w700,
                  fontSize: baseFontSize * 0.9,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: screenWidth * 0.015),
              Text(
                result.snippet,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: baseFontSize * 0.8,
                  height: 1.3,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: screenWidth * 0.015),
              Row(
                children: [
                  Icon(Icons.link, size: baseFontSize * 0.9, color: const Color(0xFF10B981)),
                  SizedBox(width: screenWidth * 0.01),
                  Expanded(
                    child: Text(
                      result.link,
                      style: TextStyle(
                        color: const Color(0xFF10B981),
                        fontSize: baseFontSize * 0.75,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon, double screenWidth, double baseFontSize) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: screenWidth * 0.15, color: Colors.grey[400]),
          SizedBox(height: screenWidth * 0.03),
          Text(
            message,
            style: TextStyle(
              fontSize: baseFontSize,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedInputPanel(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return Container(
      margin: EdgeInsets.all(screenWidth * 0.03),
      padding: EdgeInsets.all(isTablet ? screenWidth * 0.05 : screenWidth * 0.04),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        children: [
          if (controller.imageFile != null || controller.audioPath != null)
            _buildEnhancedAttachmentsPreview(controller, screenWidth, isTablet, baseFontSize),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: Container(
                  constraints: BoxConstraints(maxHeight: screenWidth * 0.25),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF9FAFB),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: const Color(0xFFE5E7EB), width: 1.2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _textController,
                    onChanged: controller.setDoubtText,
                    decoration: InputDecoration(
                      hintText: 'Ask me anything...',
                      hintStyle: TextStyle(
                        color: const Color(0xFF6B7280),
                        fontWeight: FontWeight.w500,
                        fontSize: baseFontSize * 0.9,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.03, vertical: screenWidth * 0.025),
                    ),
                    maxLines: 3,
                    minLines: 1,
                    style: TextStyle(
                      fontSize: baseFontSize * 0.9,
                      color: const Color(0xFF1F2937),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              SizedBox(width: screenWidth * 0.015),
              _buildModernAttachmentButton(
                Icons.image,
                const Color(0xFF3B82F6),
                () => _showImageChoiceDialog(context, controller),
                screenWidth,
                baseFontSize,
              ),
              SizedBox(width: screenWidth * 0.015),
              _buildModernAttachmentButton(
                controller.isRecording ? Icons.stop : Icons.mic,
                controller.isRecording
                    ? const Color(0xFFF43F5E)
                    : const Color(0xFFF59E0B),
                controller.toggleAudioRecording,
                screenWidth,
                baseFontSize,
              ),
              SizedBox(width: screenWidth * 0.015),
              _buildModernAttachmentButton(
                Icons.send,
                const Color(0xFF10B981),
                () => _handleDoubtSolver(controller),
                screenWidth,
                baseFontSize,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernAttachmentButton(
      IconData icon, Color color, VoidCallback onPressed, double screenWidth, double baseFontSize) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.15),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: baseFontSize * 1.2),
        style: IconButton.styleFrom(
          padding: EdgeInsets.all(screenWidth * 0.025),
          minimumSize: Size(screenWidth * 0.1, screenWidth * 0.1),
        ),
      ),
    );
  }

  Widget _buildEnhancedAttachmentsPreview(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return Container(
      margin: EdgeInsets.only(bottom: screenWidth * 0.03),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            if (controller.imageFile != null)
              _buildEnhancedImagePreview(controller, screenWidth, isTablet, baseFontSize),
            if (controller.audioPath != null)
              _buildEnhancedAudioPreview(controller, screenWidth, baseFontSize),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedImagePreview(
      ProblemSolverController controller, double screenWidth, bool isTablet, double baseFontSize) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Image.file(
              controller.imageFile!,
              height: isTablet ? screenWidth * 0.18 : screenWidth * 0.15,
              width: isTablet ? screenWidth * 0.22 : screenWidth * 0.18,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -2,
          right: -2,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF43F5E),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFF43F5E).withOpacity(0.3),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(Icons.close, color: Colors.white, size: baseFontSize * 0.9),
              onPressed: () {
                controller.clearAttachments();
                _showSnackbar('Success', 'Image attachment removed', ContentType.success);
              },
              style: IconButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size(screenWidth * 0.05, screenWidth * 0.05),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedAudioPreview(
      ProblemSolverController controller, double screenWidth, double baseFontSize) {
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.03, vertical: screenWidth * 0.015),
          decoration: BoxDecoration(
            color: const Color(0xFFF59E0B).withOpacity(0.15),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: const Color(0xFFF59E0B).withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFF59E0B).withOpacity(0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.audiotrack,
                  color: const Color(0xFFF59E0B), size: baseFontSize),
              SizedBox(width: screenWidth * 0.015),
              Text(
                'Audio',
                style: TextStyle(
                  color: const Color(0xFFF59E0B),
                  fontWeight: FontWeight.w600,
                  fontSize: baseFontSize * 0.9,
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: -2,
          right: -2,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF43F5E),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFF43F5E).withOpacity(0.3),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(Icons.close, color: Colors.white, size: baseFontSize * 0.9),
              onPressed: () {
                controller.clearAttachments();
                _showSnackbar('Success', 'Audio attachment removed', ContentType.success);
              },
              style: IconButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size(screenWidth * 0.05, screenWidth * 0.05),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleDoubtSolver(ProblemSolverController controller) async {
    if (controller.doubtText.isEmpty &&
        controller.imageFile == null &&
        controller.audioPath == null) {
      _showSnackbar('Warning', 'Please enter a question or attach media',
          ContentType.warning);
      return;
    }
    await controller.performSearch(SearchType.doubtSolver);
    _textController.clear();
    _showSnackbar('Success', 'Question submitted to Sparkit', ContentType.success);
  }

  Future<void> _handleSearch(
      ProblemSolverController controller, SearchType searchType) async {
    if (controller.doubtText.isEmpty) {
      _showSnackbar('Warning', 'Please enter a search query', ContentType.warning);
      return;
    }
    await controller.performSearch(searchType);
    _textController.clear();
    _showSnackbar(
        'Success',
        searchType == SearchType.youtubeSearch
            ? 'YouTube search completed'
            : 'Web search completed',
        ContentType.success);
  }

  void _showImageChoiceDialog(
      BuildContext context, ProblemSolverController controller) {
    final screenWidth = MediaQuery.of(context).size.width;
    final baseFontSize = screenWidth * 0.035;
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      backgroundColor: Colors.white,
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: EdgeInsets.all(screenWidth * 0.03),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: double.infinity,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                SizedBox(height: screenWidth * 0.04),
                ListTile(
                  leading: Icon(Icons.photo_library,
                      color: const Color(0xFF3B82F6), size: baseFontSize * 1.4),
                  title: Text(
                    'From Gallery',
                    style: TextStyle(fontSize: baseFontSize * 0.9, fontWeight: FontWeight.w600),
                  ),
                  onTap: () async {
                    await controller.pickImageFromGallery();
                    Navigator.pop(context);
                    if (controller.imageFile != null) {
                      _showSnackbar('Success', 'Image selected from gallery',
                          ContentType.success);
                    } else {
                      _showSnackbar('Error', 'Failed to select image', ContentType.failure);
                    }
                  },
                ),
                ListTile(
                  leading: Icon(Icons.camera_alt,
                      color: const Color(0xFF10B981), size: baseFontSize * 1.4),
                  title: Text(
                    'Take Photo',
                    style: TextStyle(fontSize: baseFontSize * 0.9, fontWeight: FontWeight.w600),
                  ),
                  onTap: () async {
                    await controller.captureImageWithCamera();
                    Navigator.pop(context);
                    if (controller.imageFile != null) {
                      _showSnackbar('Success', 'Photo captured', ContentType.success);
                    } else {
                      _showSnackbar('Error', 'Failed to capture photo', ContentType.failure);
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}