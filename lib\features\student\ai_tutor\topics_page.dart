// import 'package:flutter/material.dart';
// import 'dart:convert';
// import 'package:http/http.dart' as http;
// import 'dart:math' as math;
// import '../ai_tutor/sub_topics_page.dart';

// class ParticlePainter extends CustomPainter {
//   final double progress;

//   ParticlePainter(this.progress);

//   @override
//   void paint(Canvas canvas, Size size) {
//     final paint = Paint()
//       ..color = Colors.blue.shade200.withOpacity(0.5)
//       ..style = PaintingStyle.fill;

//     final random = math.Random();
//     for (int i = 0; i < 50; i++) {
//       double x = random.nextDouble() * size.width;
//       double y = random.nextDouble() * size.height;
//       double radius = random.nextDouble() * 2 + 1;
//       canvas.drawCircle(Offset(x, y), radius, paint);
//     }
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
// }

// class TopicsPage extends StatelessWidget {
//   final String subjectId;
//   final String subjectName;

//   const TopicsPage({
//     Key? key,
//     required this.subjectId,
//     required this.subjectName,
//   }) : super(key: key);

//   Future<List<dynamic>> fetchTopics() async {
//     final url = "https://sasthra.in/api/topics/$subjectId";
//     debugPrint("📡 Fetching topics from: $url");

//     try {
//       final response = await http.get(Uri.parse(url));
//       if (response.statusCode == 200) {
//         final data = jsonDecode(response.body);
//         if (data is List) {
//           debugPrint("✅ Topics fetched successfully (${data.length})");
//           return data;
//         } else {
//           debugPrint("❌ Invalid data format: Expected a list");
//           return [];
//         }
//       } else {
//         debugPrint("❌ Failed to load topics: ${response.body}");
//         return [];
//       }
//     } catch (e) {
//       debugPrint("⚠️ Exception fetching topics: $e");
//       return [];
//     }
//   }

//   Widget buildTopicCard(BuildContext context, Map<String, dynamic> topic, int index) {
//     return TweenAnimationBuilder(
//       duration: Duration(milliseconds: 400 + (index * 100)),
//       curve: Curves.easeOutBack,
//       tween: Tween<double>(begin: 0, end: 1),
//       builder: (context, double value, child) => Opacity(
//         opacity: value.clamp(0.0, 1.0), // Clamp to prevent assertion error
//         child: Transform.scale(
//           scale: value.clamp(0.0, 1.0), // Clamp scale for consistency
//           child: child,
//         ),
//       ),
//       child: Center(
//         child: Container(
//           margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
//           decoration: BoxDecoration(
//             gradient: LinearGradient(
//               colors: [Colors.blue, Colors.blue.shade800],
//               begin: Alignment.topLeft,
//               end: Alignment.bottomRight,
//             ),
//             borderRadius: BorderRadius.circular(12),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.blue.withOpacity(0.5),
//                 spreadRadius: 5,
//                 blurRadius: 15,
//                 offset: const Offset(0, 0), // Glow effect centered
//               ),
//             ],
//           ),
//           child: Padding(
//             padding: const EdgeInsets.all(10.0),
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Icon(
//                   Icons.book,
//                   size: 25,
//                   color: Colors.blue.shade200,
//                 ),
//                 const SizedBox(width: 5),
//                 Expanded(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Text(
//                         topic["topic_name"] ?? "Unnamed Topic",
//                         style: const TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                           color: Colors.white,
//                         ),
//                       ),
//                       Text(
//                         "interactive modules",
//                         style: TextStyle(
//                           fontSize: 10,
//                           color: Colors.white70,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 const SizedBox(width: 5),
//                 ElevatedButton(
//                   onPressed: () {
//                     debugPrint("➡️ Topic tapped: ${topic["topic_name"]} (ID: ${topic["topic_id"]})");
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => SubTopicsPage(
//                           topicId: topic["topic_id"],
//                           topicName: topic["topic_name"],
//                           subjectName: subjectName,
//                         ),
//                       ),
//                     );
//                   },
//                   style: ElevatedButton.styleFrom(
//                     shape: const CircleBorder(),
//                     padding: const EdgeInsets.all(8),
//                     backgroundColor: Colors.white,
//                   ),
//                   child: const Icon(Icons.arrow_forward_ios, size: 12, color: Colors.blue),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     debugPrint("🛠 Building TopicsPage for subject: $subjectName");
//     return Scaffold(
//       body: Stack(
//         children: [
//           CustomPaint(
//             painter: ParticlePainter(1.0),
//             child: Container(),
//           ),
//           FutureBuilder<List<dynamic>>(
//             future: fetchTopics(),
//             builder: (context, snapshot) {
//               if (snapshot.connectionState == ConnectionState.waiting) {
//                 return const Center(
//                   child: CircularProgressIndicator(
//                     color: Colors.white,
//                     backgroundColor: Colors.blue,
//                   ),
//                 );
//               }
//               if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
//                 return const Center(
//                   child: Text(
//                     "No topics available",
//                     style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white),
//                   ),
//                 );
//               }

//               final topics = snapshot.data!;
//               return Container(
//                 decoration: BoxDecoration(
//                   gradient: LinearGradient(
//                     colors: [const Color.fromARGB(255, 8, 50, 84), const Color.fromARGB(255, 0, 0, 0)],
//                     begin: Alignment.topCenter,
//                     end: Alignment.bottomCenter,
//                   ),
//                 ),
//                 child: Column(
//                   children: [
//                     Container(
//                       padding: const EdgeInsets.all(16.0),
//                       child: Column(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         children: [
//                           Text(
//                             "$subjectName",
//                             style: const TextStyle(
//                               fontWeight: FontWeight.bold,
//                               letterSpacing: 1.2,
//                               fontSize: 24,
//                               color: Colors.white,
//                             ),
//                           ),
//                           Text(
//                             "${topics.length} available",
//                             style: const TextStyle(
//                               fontSize: 12,
//                               color: Colors.white70,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     Expanded(
//                       child: ListView.builder(
//                         padding: const EdgeInsets.all(8.0),
//                         itemCount: topics.length,
//                         itemBuilder: (context, index) => buildTopicCard(context, topics[index], index),
//                       ),
//                     ),
//                   ],
//                 ),
//               );
//             },
//           ),
//         ],
//       ),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:math' as math;
import '../ai_tutor/sub_topics_page.dart';

class ParticlePainter extends CustomPainter {
  final double progress;

  ParticlePainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.shade200.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final random = math.Random();
    for (int i = 0; i < 50; i++) {
      double x = random.nextDouble() * size.width;
      double y = random.nextDouble() * size.height;
      double radius = random.nextDouble() * 2 + 1;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class TopicsPage extends StatelessWidget {
  final String subjectId;
  final String subjectName;

  const TopicsPage({
    Key? key,
    required this.subjectId,
    required this.subjectName,
  }) : super(key: key);

  Future<List<dynamic>> fetchTopics() async {
    final url = "https://sasthra.in/api/topics/$subjectId";
    debugPrint("📡 Fetching topics from: $url");

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data is List) {
          debugPrint("✅ Topics fetched successfully (${data.length})");
          return data;
        } else {
          debugPrint("❌ Invalid data format: Expected a list");
          return [];
        }
      } else {
        debugPrint("❌ Failed to load topics: ${response.body}");
        return [];
      }
    } catch (e) {
      debugPrint("⚠️ Exception fetching topics: $e");
      return [];
    }
  }

  Widget buildTopicCard(BuildContext context, Map<String, dynamic> topic, int index) {
    return TweenAnimationBuilder(
      duration: Duration(milliseconds: 400 + (index * 100)),
      curve: Curves.easeOutBack,
      tween: Tween<double>(begin: 0, end: 1),
      builder: (context, double value, child) => Opacity(
        opacity: value.clamp(0.0, 1.0), // Clamp to prevent assertion error
        child: Transform.scale(
          scale: value.clamp(0.0, 1.0), // Clamp scale for consistency
          child: child,
        ),
      ),
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue, Colors.blue.shade800],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 15,
                offset: const Offset(0, 0), // Glow effect centered
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.book,
                  size: 25,
                  color: Colors.blue.shade200,
                ),
                const SizedBox(width: 5),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        topic["topic_name"] ?? "Unnamed Topic",
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        "interactive modules",
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 5),
                ElevatedButton(
                  onPressed: () {
                    debugPrint("➡️ Topic tapped: ${topic["topic_name"]} (ID: ${topic["topic_id"]})");
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SubTopicsPage(
                          topicId: topic["topic_id"],
                          topicName: topic["topic_name"],
                          subjectName: subjectName,
                        ),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    shape: const CircleBorder(),
                    padding: const EdgeInsets.all(8),
                    backgroundColor: Colors.white,
                  ),
                  child: const Icon(Icons.arrow_forward_ios, size: 12, color: Colors.blue),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("🛠 Building TopicsPage for subject: $subjectName");
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          subjectName,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          CustomPaint(
            painter: ParticlePainter(1.0),
            child: Container(),
          ),
          FutureBuilder<List<dynamic>>(
            future: fetchTopics(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    backgroundColor: Colors.blue,
                  ),
                );
              }
              if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                return const Center(
                  child: Text(
                    "No topics available",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white),
                  ),
                );
              }

              final topics = snapshot.data!;
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [const Color.fromARGB(255, 8, 50, 84), const Color.fromARGB(255, 0, 0, 0)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "${topics.length} available",
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(8.0),
                        itemCount: topics.length,
                        itemBuilder: (context, index) => buildTopicCard(context, topics[index], index),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}