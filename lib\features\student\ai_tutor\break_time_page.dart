import 'dart:math';
import 'package:flutter/material.dart';

void main() {
  runApp(const ScreensaverApp());
}

class ScreensaverApp extends StatelessWidget {
  const ScreensaverApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      home: BreakTimeScreen(),
    );
  }
}

class BreakTimeScreen extends StatefulWidget {
  const BreakTimeScreen({super.key});

  @override
  State<BreakTimeScreen> createState() => _BreakTimeScreenState();
}

class _BreakTimeScreenState extends State<BreakTimeScreen> with SingleTickerProviderStateMixin {
  bool showScreensaver = true;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 60),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleScreensaver() {
    setState(() {
      showScreensaver = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          if (showScreensaver)
            Container(
              color: const Color(0xFF1E3A8A).withOpacity(0.9),
              child: CustomPaint(
                painter: ScreensaverPainter(_controller),
                size: Size.infinite,
              ),
            ),
          if (showScreensaver)
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white, size: 30),
                onPressed: _toggleScreensaver,
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black87,
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class ScreensaverPainter extends CustomPainter {
  final Animation<double> animation;
  final Random random = Random();

  ScreensaverPainter(this.animation) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final time = animation.value * 60;
    final center = Offset(size.width / 2, size.height / 2);

    // Draw cubes
    for (int i = 0; i < 20; i++) {
      final sizeFactor = 0.5 + random.nextDouble() * 0.5;
      final boxSize = 30 * sizeFactor;
      final hue = 0.5 + random.nextDouble() * 0.3;
      final color = HSLColor.fromAHSL(0.8, hue * 360, 0.7, 0.6).toColor();
      
      final x = (random.nextDouble() - 0.5) * size.width * 0.5 + sin(time + i) * 50;
      final y = (random.nextDouble() - 0.5) * size.height * 0.5 + cos(time * 0.5 + i) * 30;
      final angle = time * (random.nextDouble() - 0.5) * 0.02;

      canvas.save();
      canvas.translate(center.dx + x, center.dy + y);
      canvas.rotate(angle);
      final paint = Paint()..color = color;
      canvas.drawRect(
        Rect.fromCenter(center: Offset.zero, width: boxSize, height: boxSize),
        paint,
      );
      canvas.restore();
    }

    // Draw spheres
    for (int i = 0; i < 15; i++) {
      final radius = 15 + random.nextDouble() * 20;
      final hue = 0.1 + random.nextDouble() * 0.3;
      final color = HSLColor.fromAHSL(0.7, hue * 360, 0.8, 0.7).toColor();
      final phase = random.nextDouble() * pi * 2;
      final x = (random.nextDouble() - 0.5) * size.width * 0.4 + cos(time * 0.008 + phase) * 60;
      final y = (random.nextDouble() - 0.5) * size.height * 0.4 + sin(time * 0.008 + phase) * 90;

      canvas.drawCircle(
        Offset(center.dx + x, center.dy + y),
        radius,
        Paint()..color = color,
      );
    }

    // Draw particles
    final particlePaint = Paint()..color = Colors.white.withOpacity(0.8);
    for (int i = 0; i < 500; i++) {
      final x = (random.nextDouble() - 0.5) * size.width * 0.6;
      final y = (random.nextDouble() - 0.5) * size.height * 0.6;
      canvas.save();
      canvas.translate(center.dx, center.dy);
      canvas.rotate(time * 0.005);
      canvas.drawCircle(Offset(x, y), 2, particlePaint);
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}