import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class UnderDevelopmentPopup extends StatelessWidget {
  const UnderDevelopmentPopup({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Animated Icon
            Icon(
              Icons.construction,
              size: 64,
              color: Colors.deepPurple,
            )
                .animate()
                .scale(duration: 600.ms, curve: Curves.easeOutBack)
                .shake(duration: 400.ms, hz: 2),

            const SizedBox(height: 16),

            // Title
            const Text(
              "Under Development",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // Subtitle
            const Text(
              "This feature is currently being built.\nPlease check back soon!",
              style: TextStyle(
                fontSize: 16,
                color: Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 20),

            // Button
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () => Navigator.of(context).pop(),
              child: const Text("Got it!", style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 500.ms).scale(duration: 500.ms);
  }
}
