/// main.dart - Application Entry Point
///
/// English: This is the main entry point of the Sasthra mobile application. It handles the complete
/// application initialization process including platform-specific configurations, service initialization,
/// background task setup, and app launch. It sets up the Flutter app with Riverpod state management,
/// routing, theming, and all necessary services before the app becomes available to users.
///
/// Tanglish: Inga dhan naama app start aagum. Main function la ella initialization um nadakkum.
/// Platform specific settings, services setup, background tasks, routing - ella setup um
/// inga pannitu dhan app user ku ready aagum. Riverpod state management, theme setup,
/// authentication services - ellam inga configure pannirukkom.
///
/// Key Responsibilities:
/// - Platform-specific initialization (Web URL strategy, Mobile orientations)
/// - Local storage setup (Hive initialization)
/// - Service initialization (Auth, Storage, Token services)
/// - Background task registration (Token refresh, etc.)
/// - System UI configuration (Status bar, navigation bar)
/// - App widget creation with theme and routing
///
/// Flow: main() -> Initialize services -> Setup background tasks -> Launch SashtraApp widget
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:workmanager/workmanager.dart';
import 'package:flutter_web_plugins/url_strategy.dart';

import 'core/config/app_config.dart';
import 'core/services/app_initialization_service.dart';
import 'core/services/auth_service.dart';
import 'core/services/storage_service.dart';
import 'core/services/token_service.dart';
import 'core/providers/theme_provider.dart';
import 'core/router/app_router.dart';
import 'core/utils/logger.dart';


/// main() - Application Entry Function
///
/// English: The main function that starts the entire application. It performs all necessary
/// initialization steps before launching the Flutter app widget.
///
/// Tanglish: App start aaga vendiya main function. Ella setup um inga pannitu app launch pannum.
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  


  // Configure URL strategy for web (removes # from URLs)
  if (kIsWeb) {
    usePathUrlStrategy();
  }

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Initialize all app services
  await AppInitializationService.initialize();

  // Initialize Workmanager for background tasks (only on mobile platforms)
  if (!kIsWeb) {
    try {
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: AppConfig.isDebug,
      );

      // Register periodic token refresh task
      await Workmanager().registerPeriodicTask(
        "token-refresh-task",
        "tokenRefresh",
        frequency: const Duration(hours: 4), // Check every 4 hours
        constraints: Constraints(
          networkType: NetworkType.connected,
        ),
      );
    } catch (e) {
      AppLogger.error('Failed to initialize Workmanager: $e');
    }
  }

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  

  runApp(
    ProviderScope(
      child: SashtraApp(),
    ),
  );
}

// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case "tokenRefresh":
        await _handleTokenRefresh();
        break;
    }
    return Future.value(true);
  });
}

Future<void> _handleTokenRefresh() async {
  try {
    // Initialize services if not already done
    if (!AppInitializationService.isInitialized) {
      await AppInitializationService.initialize();
    }

    final tokenService = TokenService();
    final isValid = await tokenService.validateAndRefreshToken();

    if (!isValid) {
      // Token is invalid or expired, clear storage and redirect to login
      await StorageService.clearAll();
      AppLogger.info('Token expired, user will be redirected to login');
    }
  } catch (e) {
    AppLogger.error('Background token refresh failed: $e');
  }
}

/// SashtraApp - Main Application Widget
///
/// English: This is the root widget of the Sasthra application that extends ConsumerWidget
/// to integrate with Riverpod state management. It configures the MaterialApp with routing,
/// theming, and global app settings. It watches the app router provider and sets up the
/// complete app structure with navigation, themes, and accessibility features.
///
/// Tanglish: Inga dhan naama app oda main widget. Riverpod state management use pannitu
/// MaterialApp setup pannum. Router, theme, navigation - ella global settings um
/// inga configure pannirukkom. App oda complete structure inga define aagum.
///
/// Key Features:
/// - Riverpod state management integration
/// - Go Router navigation setup
/// - Light/Dark theme configuration
/// - Text scaling prevention for consistent UI
/// - Debug banner removal for production-ready appearance
class SashtraApp extends ConsumerWidget {
  const SashtraApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    final themeNotifier = ref.watch(themeProvider);

    // Set router in AuthService for logout navigation
    AuthService.setRouter(router);

    return MaterialApp.router(
      title: 'Sasthra',
      debugShowCheckedModeBanner: false,
      theme: themeNotifier.lightTheme,
      darkTheme: themeNotifier.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: router,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0), // Prevent text scaling
          ),
          child: child!,
        );
      },
    );
  }
}
