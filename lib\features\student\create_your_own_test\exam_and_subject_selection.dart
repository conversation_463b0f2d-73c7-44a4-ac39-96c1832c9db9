import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/config/app_config.dart';
import 'package:flutter/foundation.dart';

class AuroraFlowBackground extends StatelessWidget {
  const AuroraFlowBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[50],
      child: Stack(
        children: [
          Positioned(
            top: -MediaQuery.of(context).size.height * 0.3,
            left: -MediaQuery.of(context).size.width * 0.3,
            child: Animate(
              effects: [
                MoveEffect(
                  begin: const Offset(-0.1, -0.05),
                  end: const Offset(0.1, 0.05),
                  duration: const Duration(seconds: 30),
                  curve: Curves.easeInOut,
                ),
                ScaleEffect(
                  begin: const Offset(1, 1),
                  end: const Offset(1.1, 1.1),
                  duration: const Duration(seconds: 30),
                  curve: Curves.easeInOut,
                ),
              ],
              onPlay: (controller) => controller.repeat(reverse: true),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.6,
                height: MediaQuery.of(context).size.height * 0.6,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF2563EB).withOpacity(0.4),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2563EB).withOpacity(0.4),
                      blurRadius: 4,
                      spreadRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: -MediaQuery.of(context).size.height * 0.3,
            right: -MediaQuery.of(context).size.width * 0.3,
            child: Animate(
              effects: [
                MoveEffect(
                  begin: const Offset(0.1, 0.05),
                  end: const Offset(-0.1, -0.05),
                  duration: const Duration(seconds: 35),
                  curve: Curves.easeInOut,
                ),
                ScaleEffect(
                  begin: const Offset(1, 1),
                  end: const Offset(0.9, 0.9),
                  duration: const Duration(seconds: 35),
                  curve: Curves.easeInOut,
                ),
              ],
              delay: const Duration(seconds: 5),
              onPlay: (controller) => controller.repeat(reverse: true),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.7,
                height: MediaQuery.of(context).size.height * 0.7,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.blue[300]!.withOpacity(0.4),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2563EB).withOpacity(0.4),
                      blurRadius: 100,
                      spreadRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: MediaQuery.of(context).size.height * 0.2,
            left: MediaQuery.of(context).size.width * 0.1,
            child: Animate(
              effects: [
                MoveEffect(
                  begin: const Offset(0.05, -0.05),
                  end: const Offset(-0.05, 0.05),
                  duration: const Duration(seconds: 40),
                  curve: Curves.easeInOut,
                ),
                RotateEffect(
                  begin: 0,
                  end: 15 * math.pi / 180,
                  duration: const Duration(seconds: 40),
                  curve: Curves.easeInOut,
                ),
              ],
              delay: const Duration(seconds: 10),
              onPlay: (controller) => controller.repeat(reverse: true),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.4,
                height: MediaQuery.of(context).size.height * 0.4,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.blue[400]!.withOpacity(0.3),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2563EB).withOpacity(0.4),
                      blurRadius: 100,
                      spreadRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ExamAndSubjectSelection extends StatefulWidget {
  final String selectedExam;
  final String selectedModule;
  final ValueChanged<String> setSelectedExam;
  final ValueChanged<String> setSelectedModule;
  final String? courseName;

  const ExamAndSubjectSelection({
    super.key,
    required this.selectedExam,
    required this.selectedModule,
    required this.setSelectedExam,
    required this.setSelectedModule,
    required this.courseName,
  });

  @override
  State<ExamAndSubjectSelection> createState() =>
      _ExamAndSubjectSelectionState();
}

class _ExamAndSubjectSelectionState extends State<ExamAndSubjectSelection> {
  List<String> examNames = [];
  List<String> modules = [];
  bool isLoadingExams = true;
  bool isLoadingModules = false;
  String? examError;
  String? moduleError;

  @override
  void initState() {
    super.initState();
    _triggerExamNames();
  }

  Future<void> _triggerExamNames() async {
    setState(() => isLoadingExams = true);
    try {
      final response = await http
          .get(
            Uri.parse('${AppConfig.apiBaseUrl}${AppConfig.examNamesEndpoint}'),
            headers: AppConfig.defaultHeaders,
          )
          .timeout(AppConfig.apiTimeout);

      if (AppConfig.isDebug) {
        debugPrint(
            'API Request: ${AppConfig.apiBaseUrl}${AppConfig.examNamesEndpoint}');
        debugPrint('Status Code: ${response.statusCode}');
        debugPrint('Headers: ${response.headers}');
        debugPrint('Response Body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (AppConfig.isDebug) {
          debugPrint('Parsed JSON Data: $data');
        }
        List<String> fetchedExams = List<String>.from(
            data['exam_names'].map((exam) => exam['exam_name']));
        setState(() {
          examNames = _filteredExams(fetchedExams,widget.courseName);
          isLoadingExams = false;
        });
      } else {
        if (AppConfig.isDebug) {
          debugPrint(
              'Error: Failed to load exams with status code ${response.statusCode}');
        }
        setState(() {
          examError = 'Failed to load exams: ${response.statusCode}';
          isLoadingExams = false;
        });
      }
    } catch (e) {
      if (AppConfig.isDebug) {
        debugPrint('Exception: Error fetching exams: ${e.toString()}');
      }
      setState(() {
        examError = 'Error fetching exams: ${e.toString()}';
        isLoadingExams = false;
      });
    }
  }


  List<String> _filteredExams(List<String> exams, String? courseName) {
    if(courseName=='JEE'){
      return exams.where((exam)=>exam=="JEE MAINS" || exam=="JEE ADVANCED").toList();
    }
    else if(courseName=="NEET"){
      return exams.where((exam)=>exam=="NEET").toList();
    }
    else{
      return exams;
    }
  }

  Future<void> _triggerModules(String examName) async {
    setState(() => isLoadingModules = true);
    try {
      final response = await http
          .get(
            Uri.parse(
                '${AppConfig.apiBaseUrl}${AppConfig.examModulesEndpoint}?exam_name=$examName'),
            headers: AppConfig.defaultHeaders,
          )
          .timeout(AppConfig.apiTimeout);

      if (AppConfig.isDebug) {
        debugPrint(
            'API Request: ${AppConfig.apiBaseUrl}${AppConfig.examModulesEndpoint}?exam_name=$examName');
        debugPrint('Status Code: ${response.statusCode}');
        debugPrint('Headers: ${response.headers}');
        debugPrint('Response Body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (AppConfig.isDebug) {
          debugPrint('Parsed JSON Data: $data');
        }
        setState(() {
          modules = List<String>.from(data['subjects']);
          isLoadingModules = false;
        });
      } else {
        if (AppConfig.isDebug) {
          debugPrint(
              'Error: Failed to load modules with status code ${response.statusCode}');
        }
        setState(() {
          moduleError = 'Failed to load modules: ${response.statusCode}';
          isLoadingModules = false;
        });
      }
    } catch (e) {
      if (AppConfig.isDebug) {
        debugPrint('Exception: Error fetching modules: ${e.toString()}');
      }
      setState(() {
        moduleError = 'Error fetching modules: ${e.toString()}';
        isLoadingModules = false;
      });
    }
  }

  @override
  void didUpdateWidget(covariant ExamAndSubjectSelection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedExam != oldWidget.selectedExam &&
        widget.selectedExam.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _triggerModules(widget.selectedExam);
        widget.setSelectedModule('');
      });
    }
  }

  Map<String, IconData> subjectIcons = {
    'Physics': FontAwesomeIcons.atom,
    'Chemistry': FontAwesomeIcons.flaskVial,
    'Biology': FontAwesomeIcons.dna,
    'Mathematics': FontAwesomeIcons.squareRootAlt,
  };

  Map<String, IconData> examIcons = {
    'JEE': FontAwesomeIcons.gears,
    'NEET': FontAwesomeIcons.stethoscope,
  };

  Widget renderLoader(String text) {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(FontAwesomeIcons.spinner, size: 24, color: Color(0xFF2563EB))
              .animate(
                onPlay: (controller) => controller.repeat(),
              )
              .rotate(
                  duration: const Duration(seconds: 1), curve: Curves.linear),
          const SizedBox(width: 12),
          Text(text,
              style: TextStyle(fontSize: 18, color: Colors.grey.shade500)),
        ],
      ),
    );
  }

  Widget renderError(String? error, String message) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          border: Border.all(color: Colors.red.shade200),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(FontAwesomeIcons.triangleExclamation,
                color: Colors.red[700], size: 20),
            const SizedBox(width: 12),
            Text(error ?? message,
                style: TextStyle(color: Colors.red.shade700)),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          const AuroraFlowBackground(),
          SafeArea(
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 1280),
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 600),
                  transitionBuilder: (child, animation) =>
                      FadeTransition(opacity: animation, child: child),
                  child: widget.selectedExam.isEmpty
                      ? _buildExamSelection()
                      : _buildSubjectSelection(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExamSelection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'Create Your Own Test',
          style: TextStyle(
              fontSize: 40, fontWeight: FontWeight.bold, color: Colors.black87),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          'Select your exam stream to begin.',
          style: TextStyle(fontSize: 18, color: Colors.grey[600]),
        ),
        const SizedBox(height: 48),
        if (isLoadingExams) renderLoader('Initializing streams...'),
        if (examError != null)
          renderError(examError, 'Stream initialization failed'),
        if (!isLoadingExams && examError == null)
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 48,
            runSpacing: 48,
            children: examNames.asMap().entries.map((entry) {
              int i = entry.key;
              String exam = entry.value;
              return GestureDetector(
                onTap: () => widget.setSelectedExam(exam),
                child: Animate(
                  effects: [
                    FadeEffect(
                        delay: Duration(milliseconds: i * 150),
                        duration: const Duration(milliseconds: 300)),
                    MoveEffect(
                        begin: const Offset(0, 50),
                        end: Offset.zero,
                        delay: Duration(milliseconds: i * 150),
                        duration: const Duration(milliseconds: 300)),
                  ],
                  child: Container(
                    width: 256,
                    height: 160,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.white.withOpacity(0.2)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FaIcon(
                          examIcons[exam] ?? FontAwesomeIcons.gears,
                          size: 48,
                          color: const Color(0xFF2563EB),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          exam,
                          style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildSubjectSelection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () => widget.setSelectedExam(''),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const FaIcon(FontAwesomeIcons.chevronLeft,
                  color: Color(0xFF2563EB)),
              const SizedBox(width: 8),
              const Text(
                'Change Exam Stream',
                style: TextStyle(
                    color: Color(0xFF2563EB),
                    fontSize: 14,
                    fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
        Text(
          'Select Subject for ${widget.selectedExam}',
          style: const TextStyle(
              fontSize: 32, fontWeight: FontWeight.bold, color: Colors.black87),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 64),
        if (isLoadingModules) renderLoader('Loading subject nodes...'),
        if (moduleError != null)
          renderError(moduleError, 'Could not load nodes'),
        if (!isLoadingModules && moduleError == null)
          SizedBox(
            width: 384,
            height: 384,
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                Animate(
                  effects: [
                    ScaleEffect(
                      begin: const Offset(
                          1.0, 1.0), // Use Offset for compatibility
                      end: const Offset(1.05, 1.05),
                      duration: const Duration(seconds: 3),
                      curve: Curves.easeInOut,
                    ),
                  ],
                  onPlay: (controller) => controller.repeat(reverse: true),
                  child: Container(
                    width: 128,
                    height: 128,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          const Color(0xFF2563EB),
                          const Color(0xFF1E40AF)
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF2563EB),
                          blurRadius: 20,
                        ),
                        BoxShadow(
                          color: const Color(0xFF2563EB),
                          blurRadius: 40,
                        ),
                        BoxShadow(
                          color: const Color(0xFF1E40AF),
                          blurRadius: 60,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        widget.selectedExam,
                        style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(blurRadius: 2, color: Colors.black)
                            ]),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                ...modules.asMap().entries.map((entry) {
                  int i = entry.key;
                  String module = entry.value;
                  final angle =
                      (i / modules.length) * 2 * math.pi - math.pi / 2;
                  final radius = 180.0;
                  final x = radius * math.cos(angle);
                  final y = radius * math.sin(angle);
                  final isSelected = widget.selectedModule == module;

                  return Positioned(
                      left: 192 + x - 64, // Center is at 192,192 for 384x384
                      top: 192 + y - 64,
                      child: GestureDetector(
                        onTap: () => widget.setSelectedModule(module),
                        child: Animate(
                          effects: [
                            FadeEffect(
                                delay: Duration(milliseconds: i * 100),
                                duration: const Duration(milliseconds: 300)),
                            MoveEffect(
                                begin: const Offset(0, 20),
                                end: Offset.zero,
                                delay: Duration(milliseconds: i * 100),
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.bounceOut),
                          ],
                          child: SizedBox(
                            width: 128,
                            height: 128,
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: isSelected
                                    ? const Color(0xFF2563EB)
                                    : Colors.white.withOpacity(0.8),
                                border: Border.all(
                                  color: isSelected
                                      ? Colors.blue.shade300
                                      : Colors.white.withOpacity(0.2),
                                  width: isSelected ? 2 : 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FaIcon(
                                    subjectIcons[module] ??
                                        FontAwesomeIcons.book,
                                    size: 32,
                                    color: isSelected
                                        ? Colors.white
                                        : const Color(0xFF2563EB),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    module,
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: isSelected
                                          ? Colors.white
                                          : Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ));
                }).toList(),
              ],
            ),
          ),
      ],
    );
  }
}
