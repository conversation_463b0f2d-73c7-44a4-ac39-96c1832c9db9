import 'dart:io';
import 'package:http/http.dart' as http;
import '../../../../core/config/app_config.dart';
import '../../../../core/services/api_service.dart';
import 'package:flutter/foundation.dart';

class FaceVerificationModel {
  final ApiService _apiService;

  FaceVerificationModel(this._apiService);

  Future<Map<String, dynamic>> verifyFace({
    required String userId,
    required File imageFile,
  }) async {
    try {
      final result = await _apiService.verifyFace(
        userId: userId,
        imageFile: imageFile,
      );

      if (AppConfig.isDebug) {
        debugPrint('API Request: ${AppConfig.apiBaseUrl}/verify-face');
        debugPrint('Status Code: 200');
        debugPrint('Response Body: $result');
      }

      return result;
    } catch (e) {
      if (AppConfig.isDebug) {
        debugPrint('API Request: ${AppConfig.apiBaseUrl}/verify-face');
        debugPrint('Error: $e');
      }
      throw Exception('Failed to verify face: $e');
    }
  }
}