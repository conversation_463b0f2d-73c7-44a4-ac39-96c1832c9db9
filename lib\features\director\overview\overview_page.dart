/// overview_page.dart - Director Overview Dashboard Page
///
/// English: This page serves as the main dashboard for Director role users in the Sasthra
/// application. It provides a comprehensive overview of the institution's performance,
/// statistics, and key metrics. Directors can view center performance, student enrollment,
/// faculty statistics, revenue metrics, and quick access to important management functions.
///
/// Tanglish: Inga Director role user ku main dashboard page irukku. Institution oda
/// performance, statistics, key metrics - ellam inga overview aaga kaatirukkom.
/// Center performance, student count, faculty details, revenue - ella important
/// data um oru place la paakka mudiyum.
///
/// Key Features:
/// - Institution performance metrics and KPIs
/// - Center-wise performance analytics
/// - Student enrollment and faculty statistics
/// - Revenue and financial overview
/// - Quick action buttons for common tasks
/// - Real-time data updates with loading states
/// - Responsive design with animations
///
/// UI Components:
/// - Statistics cards with animated counters
/// - Performance charts and graphs
/// - Quick action grid for navigation
/// - Recent activities timeline
/// - Alert notifications for important updates
library;

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/providers/theme_provider.dart';
import '../../../core/utils/logger.dart';

/// DirectorOverviewPage - Main Dashboard Widget for Directors
///
/// English: ConsumerStatefulWidget that displays the director's main dashboard with overview data.
/// Tanglish: Director ku main dashboard kaatira vendiya ConsumerStatefulWidget.
class DirectorOverviewPage extends ConsumerStatefulWidget {
  const DirectorOverviewPage({super.key});

  @override
  ConsumerState<DirectorOverviewPage> createState() => _DirectorOverviewPageState();
}

class _DirectorOverviewPageState extends ConsumerState<DirectorOverviewPage> {
  bool _isLoading = false;
  Map<String, dynamic> _overviewData = {};

  @override
  void initState() {
    super.initState();
    _loadOverviewData();
  }

  Future<void> _loadOverviewData() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _overviewData = {
        'stats': {
          'totalCenters': 45,
          'totalTeachers': 156,
          'totalStudents': 2340,
          'activeBatches': 28,
        },
        'recentActivities': [
          {'type': 'center', 'message': 'New center added in Mumbai', 'time': '2 hours ago'},
          {'type': 'teacher', 'message': 'Kota teacher onboarded', 'time': '4 hours ago'},
          {'type': 'batch', 'message': 'JEE 2024 batch started', 'time': '1 day ago'},
        ],
        'pendingApprovals': [
          {'type': 'Center Registration', 'count': 5},
          {'type': 'Teacher Applications', 'count': 12},
          {'type': 'Course Updates', 'count': 3},
        ],
      };
      _isLoading = false;
    });
    
    AppLogger.userAction('Director overview loaded', <String, dynamic>{});
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Director Overview',
      subtitle: 'Manage your educational network',
      breadcrumbs: const ['Dashboard', 'Director', 'Overview'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadOverviewData,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      child: _buildOverviewContent(),
    );
  }

  Widget _buildOverviewContent() {
    if (_overviewData.isEmpty) return const SizedBox.shrink();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsGrid(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildRecentActivities()),
              const SizedBox(width: 16),
              Expanded(child: _buildPendingApprovals()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    final stats = _overviewData['stats'] as Map<String, dynamic>;
    final roleColor = ref.watch(currentRoleColorProvider);

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard('Total Centers', '${stats['totalCenters']}', Icons.location_city, roleColor),
        _buildStatCard('Kota Teachers', '${stats['totalTeachers']}', Icons.school, roleColor.withValues(alpha: 0.8)),
        _buildStatCard('Total Students', '${stats['totalStudents']}', Icons.people, roleColor.withValues(alpha: 0.6)),
        _buildStatCard('Active Batches', '${stats['activeBatches']}', Icons.class_, roleColor.withValues(alpha: 0.4)),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: AppTheme.headingLarge.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildActionCard('Add Center', Icons.add_location, () => _addCenter()),
            _buildActionCard('Add Teacher', Icons.person_add, () => _addTeacher()),
            _buildActionCard('Create Batch', Icons.group_add, () => _createBatch()),
            _buildActionCard('View Reports', Icons.analytics, () => _viewReports()),
            _buildActionCard('Manage Courses', Icons.book, () => _manageCourses()),
            _buildActionCard('Settings', Icons.settings, () => _openSettings()),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.borderColor),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: ref.watch(currentRoleColorProvider), size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildRecentActivities() {
    final activities = _overviewData['recentActivities'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activities',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activities.length,
            itemBuilder: (context, index) {
              final activity = activities[index];
              return ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getActivityColor(activity['type']).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    _getActivityIcon(activity['type']),
                    color: _getActivityColor(activity['type']),
                    size: 20,
                  ),
                ),
                title: Text(
                  activity['message'],
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                  ),
                ),
                subtitle: Text(
                  activity['time'],
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPendingApprovals() {
    final approvals = _overviewData['pendingApprovals'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pending Approvals',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: approvals.length,
            itemBuilder: (context, index) {
              final approval = approvals[index];
              return ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: ref.watch(currentRoleColorProvider).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.pending_actions,
                    color: ref.watch(currentRoleColorProvider),
                    size: 20,
                  ),
                ),
                title: Text(
                  approval['type'],
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                  ),
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${approval['count']}',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.errorColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                onTap: () => _handleApproval(approval['type']),
              );
            },
          ),
        ),
      ],
    );
  }

  Color _getActivityColor(String type) {
    final roleColor = ref.watch(currentRoleColorProvider);
    switch (type) {
      case 'center':
        return roleColor;
      case 'teacher':
        return roleColor.withValues(alpha: 0.8);
      case 'batch':
        return roleColor.withValues(alpha: 0.6);
      default:
        return roleColor;
    }
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'center':
        return Icons.location_city;
      case 'teacher':
        return Icons.person;
      case 'batch':
        return Icons.group;
      default:
        return Icons.circle;
    }
  }

  void _addCenter() {
    AppLogger.userAction('Add center clicked', <String, dynamic>{});
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add Center feature coming soon!')),
    );
  }

  void _addTeacher() {
    AppLogger.userAction('Add teacher clicked', <String, dynamic>{});
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add Teacher feature coming soon!')),
    );
  }

  void _createBatch() {
    AppLogger.userAction('Create batch clicked', <String, dynamic>{});
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create Batch feature coming soon!')),
    );
  }

  void _viewReports() {
    AppLogger.userAction('View reports clicked', <String, dynamic>{});
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('View Reports feature coming soon!')),
    );
  }

  void _manageCourses() {
    AppLogger.userAction('Manage courses clicked', <String, dynamic>{});
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Manage Courses feature coming soon!')),
    );
  }

  void _openSettings() {
    AppLogger.userAction('Settings clicked', <String, dynamic>{});
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings feature coming soon!')),
    );
  }

  void _handleApproval(String type) {
    AppLogger.userAction('Approval clicked', <String, dynamic>{'type': type});
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$type approval feature coming soon!')),
    );
  }
}
