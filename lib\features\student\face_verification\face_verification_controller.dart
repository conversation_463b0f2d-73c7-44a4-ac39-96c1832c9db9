import 'dart:async';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'face_verification_model.dart';
import '../../../../core/config/app_config.dart';
import 'face_verification_view.dart';

class FaceVerificationController implements IFaceVerificationController {
  final FaceVerificationModel model;
  CameraController? _controller;
  XFile? _capturedImage;
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, dynamic>? _res;
  Timer? _autoCloseTimer;

  FaceVerificationController(this.model);

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic>? get res => _res;
  XFile? get capturedImage => _capturedImage;
  CameraController? get controller => _controller;
  bool get isVerifying => _isLoading;
  CameraController? get camera => _controller;

  Future<void> initCamera() async {
    try {
      if (AppConfig.isDebug) {
        print('Initializing camera...');
      }
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('No cameras available');
      }
      final frontCamera = cameras.firstWhere(
        (desc) => desc.lensDirection == CameraLensDirection.front,
        orElse: () => cameras.first,
      );
      _controller = CameraController(frontCamera, ResolutionPreset.high);
      await _controller!.initialize();
      if (AppConfig.isDebug) {
        print('Camera initialized successfully');
      }
    } catch (e) {
      _errorMessage = 'Error accessing camera: $e';
      if (AppConfig.isDebug) {
        print(_errorMessage);
      }
      _autoCloseTimer = Timer(const Duration(seconds: 2), () {
        onVerificationComplete?.call(false);
      });
    }
  }

  Future<void> captureImage() async {
    try {
      if (_controller == null || !_controller!.value.isInitialized) {
        _errorMessage = 'Camera not initialized.';
        if (AppConfig.isDebug) {
          print(_errorMessage);
        }
        _autoCloseTimer = Timer(const Duration(seconds: 2), () {
          onVerificationComplete?.call(false);
        });
        return;
      }
      if (AppConfig.isDebug) {
        print('Capturing image...');
      }
      final image = await _controller!.takePicture();
      _capturedImage = image;
      if (AppConfig.isDebug) {
        print('Image captured: ${image.path}');
      }
    } catch (e) {
      _errorMessage = 'Error capturing image: $e';
      if (AppConfig.isDebug) {
        print(_errorMessage);
      }
      _autoCloseTimer = Timer(const Duration(seconds: 2), () {
        onVerificationComplete?.call(false);
      });
    }
  }

  Future<bool> submitVerification(String userId) async {
    if (_capturedImage == null) {
      _errorMessage = 'Please capture an image first.';
      if (AppConfig.isDebug) {
        print(_errorMessage);
      }
      _autoCloseTimer = Timer(const Duration(seconds: 2), () {
        onVerificationComplete?.call(false);
      });
      
      return false;
    }

    if (userId.isEmpty) {
      _errorMessage = 'No user ID found. Please log in.';
      if (AppConfig.isDebug) {
        print(_errorMessage);
      }
      _autoCloseTimer = Timer(const Duration(seconds: 2), () {
        onVerificationComplete?.call(false);
      });
      return false;

    }

    _isLoading = true;
    _errorMessage = null;
   
    if (AppConfig.isDebug) {
      print('Submitting face verification for userId: $userId, image: ${_capturedImage!.path}');
    }

    try {
      final result = await model.verifyFace(
        userId: userId,
        imageFile: File(_capturedImage!.path),
      );

      _res = result;
      _isLoading = false;

      if (AppConfig.isDebug) {
        print('Verification API response: $result');
      }

      if (result['success'] == true) {
        _autoCloseTimer = Timer(const Duration(seconds: 2), () {
          onVerificationComplete?.call(true);
        });
        return true;
      } else {
        _errorMessage = result['message'] ?? 'Face does not match.';
        if (AppConfig.isDebug) {
          print('Verification failed: $_errorMessage');
        }
        _autoCloseTimer = Timer(const Duration(seconds: 2), () {
          onVerificationComplete?.call(false);
        });
        return false;
      }
    } catch (e) {
      _errorMessage = 'Error verifying face: $e';
      if (AppConfig.isDebug) {
        print(_errorMessage);
      }
      _isLoading = false;
      _autoCloseTimer = Timer(const Duration(seconds: 2), () {
        onVerificationComplete?.call(false);
      });
      return false;
    }
    
  }

  Future<void> disposeCamera() async {
    if (AppConfig.isDebug) {
      print('Disposing FaceVerificationController');
    }
    _autoCloseTimer?.cancel();
    _controller?.dispose();
  }

  Function(bool)? onVerificationComplete;
}