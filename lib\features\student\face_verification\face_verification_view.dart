import 'dart:async';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

/// A simple controller contract you can adapt to your existing implementation.
/// Replace with your own controller if you already have one.
abstract class IFaceVerificationController {
  CameraController? get camera;
  bool get isVerifying;
  Object? get capturedImage; // e.g., XFile or your type
  String? get errorMessage;

  Future<void> initCamera();
  Future<void> disposeCamera();
  Future<void> captureImage();
  Future<bool> submitVerification(String userId);
}

/// Example UI widget. Plug in your own controller that implements IFaceVerificationController.
class FaceVerificationView extends StatefulWidget {
  final IFaceVerificationController controller;
  final String userId;
  final ValueChanged<bool>? onVerificationComplete;

  /// If true, tapping Stop will intentionally throw and be caught to show error UI.
  /// Handy to verify that error SnackBars and guards work as intended.
  final bool forceErrorOnStop;

  const FaceVerificationView({
    super.key,
    required this.controller,
    required this.userId,
    this.onVerificationComplete,
    this.forceErrorOnStop = false,
  });

  @override
  State<FaceVerificationView> createState() => _FaceVerificationViewState();
}

class _FaceVerificationViewState extends State<FaceVerificationView> {
  bool _cameraActive = false; // guards preview to prevent buildPreview on disposed controller
  bool _initializing = true;  // subtle init state separate from verification
  bool _mounted = true;

  CameraController? get _cam => widget.controller.camera;
  bool get _isCameraReady => _cameraActive && _cam != null && _cam!.value.isInitialized;

  @override
  void initState() {
    super.initState();
    _initCamera();
  }

  @override
  void dispose() {
    _mounted = false;
    _stopCameraSilently();
    super.dispose();
  }

  Future<void> _initCamera() async {
    setState(() {
      _initializing = true;
    });
    try {
      await widget.controller.initCamera();
      if (!_mounted) return;
      setState(() {
        _cameraActive = true;
      });
    } catch (e) {
      _showSnack(
        title: 'Camera Error',
        message: e.toString(),
        type: ContentType.failure,
      );
    } finally {
      if (_mounted) {
        setState(() {
          _initializing = false;
        });
      }
    }
  }

  Future<void> _startCamera() async {
    if (_cameraActive) return;
    await _initCamera();
  }

  Future<void> _stopCamera() async {
    try {
      // Optionally simulate/force an error so you can verify the UI path.
      if (widget.forceErrorOnStop) {
        throw CameraException('debug_stop', 'Forced exception on Stop for verification.');
      }

      await widget.controller.disposeCamera();
      if (!_mounted) return;
      setState(() {
        _cameraActive = false;
      });
      _showSnack(
        title: 'Camera Stopped',
        message: 'The camera has been stopped safely.',
        type: ContentType.warning,
      );
    } catch (e) {
      _showSnack(
        title: 'Stop Failed',
        message: e.toString(),
        type: ContentType.failure,
      );
    }
  }

  Future<void> _stopCameraSilently() async {
    try {
      await widget.controller.disposeCamera();
    } catch (_) {
      // ignore on dispose
    } finally {
      _cameraActive = false;
    }
  }

  Future<void> _capture() async {
    try {
      await widget.controller.captureImage();
      setState(() {});
      _showSnack(
        title: 'Captured',
        message: 'Preview captured. You can verify now.',
        type: ContentType.success,
      );
    } catch (e) {
      _showSnack(
        title: 'Capture Error',
        message: e.toString(),
        type: ContentType.failure,
      );
    }
  }

  Future<void> _verify() async {
    if (widget.controller.capturedImage == null) {
      _showSnack(
        title: 'No Image',
        message: 'Please capture your face before verifying.',
        type: ContentType.warning,
      );
      return;
    }
    setState(() {}); // reflect verifying state via controller.isVerifying if you expose it

    try {
      final ok = await widget.controller.submitVerification(widget.userId);
      if (!_mounted) return;

      _showSnack(
        title: ok ? 'Verified' : 'Not Verified',
        message: ok ? 'You can start the exam.' : 'Your identity could not be verified.',
        type: ok ? ContentType.success : ContentType.failure,
      );
      widget.onVerificationComplete?.call(ok);
    } catch (e) {
      _showSnack(
        title: 'Verify Error',
        message: e.toString(),
        type: ContentType.failure,
      );
    } finally {
      if (_mounted) setState(() {});
    }
  }

  void _showSnack({
    required String title,
    required String message,
    required ContentType type,
  }) {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: title,
        message: message,
        contentType: type,
      ),
    );
    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    // MediaQuery + LayoutBuilder-driven responsiveness
    final mq = MediaQuery.of(context);
    final size = mq.size;
    final isLandscape = size.width > size.height;
    final shortest = size.shortestSide;
    final isPhoneCompact = shortest < 360;       // very small phones
    final isPhone = shortest < 600;              // phones
    final isTablet = shortest >= 600 && shortest < 900;
    final isDesktop = shortest >= 900;

    // Preview sizing based on device category
    final double previewDiameter = isDesktop
        ? 380
        : isTablet
            ? 320
            : isPhoneCompact
                ? 200
                : 260;

    final theme = Theme.of(context);
    final cs = theme.colorScheme;

    return SafeArea(
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        backgroundColor: Colors.transparent,
        child: LayoutBuilder(
          builder: (context, constraints) {
            final isWide = constraints.maxWidth >= 720;
            final cardPadding = EdgeInsets.symmetric(
              horizontal: isDesktop || isTablet ? 24 : 16,
              vertical: isDesktop || isTablet ? 20 : 14,
            );

            final content = <Widget>[
              // Title + Close
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'Face Verification',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'Align your face within the frame and verify your identity.',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    tooltip: 'Close',
                    icon: Icon(Icons.close, color: cs.onSurface.withOpacity(0.8)),
                    onPressed: () => widget.onVerificationComplete?.call(false),
                  ),
                ],
              ),
              SizedBox(height: isPhone ? 12 : 16),

              // Main content area
              if (isWide) ...[
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _buildPreview(previewDiameter, cs)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildControls(theme, cs)),
                  ],
                ),
              ] else ...[
                _buildPreview(previewDiameter, cs),
                SizedBox(height: isPhone ? 14 : 18),
                _buildControls(theme, cs),
              ],
            ];

            return Container(
              decoration: BoxDecoration(
                color: cs.surface,
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cs.surface,
                    cs.surfaceVariant.withOpacity(0.35),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 22,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 980),
                child: Padding(
                  padding: cardPadding,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: content,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPreview(double diameter, ColorScheme cs) {
    final theme = Theme.of(context);
    final border = Border.all(color: cs.primary.withOpacity(0.2), width: 2);

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Circular camera preview with guards
          Container(
            width: diameter,
            height: diameter,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: border,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.10),
                  blurRadius: 14,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: ClipOval(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  if (_isCameraReady)
                    CameraPreview(_cam!)
                  else
                    Container(
                      color: cs.surfaceVariant.withOpacity(0.35),
                      alignment: Alignment.center,
                      child: _initializing
                          ? SpinKitPulse(color: cs.primary, size: 34)
                          : Icon(Icons.videocam_off, color: cs.onSurface.withOpacity(0.6), size: 42),
                    ),

                  // Verification overlay—only during verification
                  if (widget.controller.isVerifying)
                    Container(
                      color: Colors.black.withOpacity(0.22),
                      alignment: Alignment.center,
                      child: SpinKitThreeBounce(color: Colors.white, size: 22),
                    ),

                  // Captured state overlay (subtle)
                  if (widget.controller.capturedImage != null)
                    IgnorePointer(
                      child: Container(
                        color: Colors.black.withOpacity(0.10),
                        alignment: Alignment.bottomCenter,
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.55),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Captured',
                            style: theme.textTheme.labelSmall?.copyWith(color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            _isCameraReady ? 'Make sure your face is well-lit and centered.' : 'Start the camera to begin.',
            style: theme.textTheme.bodySmall?.copyWith(color: theme.textTheme.bodySmall?.color?.withOpacity(0.8)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildControls(ThemeData theme, ColorScheme cs) {
    final onPrimary = cs.onPrimary;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Capture / Verify
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: widget.controller.isVerifying ? null : _capture,
                style: ElevatedButton.styleFrom(
                  backgroundColor: cs.surfaceVariant,
                  foregroundColor: cs.onSurface,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text(
                  widget.controller.capturedImage == null ? 'Capture' : 'Retake',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: widget.controller.isVerifying ? null : _verify,
                style: ElevatedButton.styleFrom(
                  backgroundColor: cs.primary,
                  foregroundColor: onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: widget.controller.isVerifying
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpinKitThreeBounce(color: onPrimary, size: 18),
                          const SizedBox(width: 8),
                          const Text('Verifying...', style: TextStyle(fontWeight: FontWeight.w600)),
                        ],
                      )
                    : const Text('Verify', style: TextStyle(fontWeight: FontWeight.w600)),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Start / Stop
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _isCameraReady ? null : _startCamera,
                icon: const Icon(Icons.play_arrow),
                label: const Text('Start Camera'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: !_isCameraReady ? null : _stopCamera,
                icon: const Icon(Icons.stop_circle_outlined),
                label: const Text('Stop'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  foregroundColor: cs.error,
                  side: BorderSide(color: cs.error.withOpacity(0.6)),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Cancel
        SizedBox(
          width: double.infinity,
          child: TextButton.icon(
            onPressed: () => widget.onVerificationComplete?.call(false),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Cancel'),
            style: TextButton.styleFrom(
              foregroundColor: theme.colorScheme.onSurface.withOpacity(0.85),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
        ),

        // Inline error (optional)
        if (widget.controller.errorMessage != null) ...[
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              widget.controller.errorMessage!,
              style: TextStyle(color: cs.error, fontSize: 13),
            ),
          ),
        ],
      ],
    );
  }
}