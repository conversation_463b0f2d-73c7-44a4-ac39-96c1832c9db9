import 'dart:async';
import 'dart:convert';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../../core/config/app_config.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/token_service.dart';
import '../../../core/utils/logger.dart';
import 'incoming_call.dart';

class MentorDashboard extends StatefulWidget {
  const MentorDashboard({super.key});

  @override
  State<MentorDashboard> createState() => _MentorDashboardState();
}

class _MentorDashboardState extends State<MentorDashboard> {
  final String apiBaseUrl = AppConfig.baseUrl;
  final String livekitUrl = 'wss://livekit.sasthra.in';

  String? mentorId;
  String? authToken;
  String? sessionId;
  Map<String, dynamic>? userData;
  String currentStatus = 'online';
  bool isStatusOpen = false;
  Map<String, dynamic>? mentorData;
  bool isLoading = true;
  String? errorMessage;
  bool isSessionLoading = false;
  Timer? pollingTimer;
  Map<String, dynamic>? incomingCall;
  bool showIncomingCall = false;
  bool isCallAccepting = false;
  final AudioPlayer ringtonePlayer = AudioPlayer();
  String currentTime = '';

  final List<Map<String, dynamic>> statusOptions = [
    {
      'value': 'online',
      'label': 'Online',
      'color': Colors.green[400],
      'pulseColor': Colors.green.withAlpha(178),
    },
    {
      'value': 'set_away',
      'label': 'Away',
      'color': Colors.yellow[400],
      'pulseColor': Colors.yellow.withAlpha(178),
    },
    {
      'value': 'offline',
      'label': 'Offline',
      'color': Colors.red[400],
      'pulseColor': Colors.red.withAlpha(178),
    },
  ];

  // Mock data for student progress
  final List<Map<String, dynamic>> studentProgress = [
    {'name': 'Alice Johnson', 'progress': 85, 'avatarColor': Colors.pink[500]},
    {'name': 'Bob Smith', 'progress': 72, 'avatarColor': Colors.blue[500]},
    {'name': 'Charlie Brown', 'progress': 93, 'avatarColor': Colors.green[500]},
    {'name': 'Diana Prince', 'progress': 68, 'avatarColor': Colors.purple[500]},
  ];

  // Mock upcoming sessions
  final List<Map<String, dynamic>> upcomingSessions = [
    {'title': 'Advanced React Patterns', 'time': 'Today, 4:00 PM', 'status': 'upcoming'},
    {'title': 'State Management Deep Dive', 'time': 'Wed, 4:00 PM', 'status': 'scheduled'},
    {'title': 'Project Review Session', 'time': 'Fri, 4:00 PM', 'status': 'scheduled'},
  ];

  @override
  void initState() {
    super.initState();
    _loadMentorData();
    _updateTime();
    Timer.periodic(const Duration(minutes: 1), (_) => _updateTime());
  }

  void _updateTime() {
    final now = DateTime.now();
    setState(() {
      currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    });
  }

  Future<void> _loadMentorData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Get user data from storage
      userData = StorageService.getObject(AppConfig.userDataKey);
      if (userData == null) {
        setState(() {
          errorMessage = 'User data not found. Please log in again.';
          isLoading = false;
        });
        return;
      }

      mentorId = userData!['id']?.toString();
      if (mentorId == null || mentorId!.isEmpty) {
        setState(() {
          errorMessage = 'Mentor ID not found. Please log in again.';
          isLoading = false;
        });
        return;
      }

      // Get auth token
      authToken = await TokenService().getToken();
      if (authToken == null) {
        setState(() {
          errorMessage = 'Authentication token not found. Please log in again.';
          isLoading = false;
        });
        return;
      }

      // Get session ID
      sessionId = await TokenService().getSessionId();

      AppLogger.info('Mentor data loaded: ID=$mentorId');

      // Automatically set to online on init
      await _handleStatusChange('online');

      // Fetch mentor dashboard data
      await _fetchMentorDashboardData();

    } catch (e) {
      AppLogger.error('Error loading mentor data: $e');
      setState(() {
        errorMessage = 'Error loading data: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _fetchMentorDashboardData() async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/mentor-dashboard'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
      );

      if (response.statusCode == 200) {
        setState(() {
          mentorData = jsonDecode(response.body);
          isLoading = false;
        });
        AppLogger.info('Mentor dashboard data fetched successfully');
      } else {
        setState(() {
          errorMessage = 'Failed to load mentor data: ${response.statusCode}';
          isLoading = false;
        });
        AppLogger.error('Failed to fetch mentor dashboard data: ${response.statusCode}');
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Error fetching mentor data: $e';
        isLoading = false;
      });
      AppLogger.error('Error fetching mentor dashboard data: $e');
    }
  }

  Future<void> _handleStatusChange(String status) async {
    if (mentorId == null || authToken == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Mentor data not available. Please log in again.')),
        );
      }
      return;
    }

    setState(() => isSessionLoading = true);

    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/mentor-session'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'action': status,
          'mentor_id': mentorId,
          'session_id': sessionId,
        }),
      );

      if (response.statusCode == 200) {
        setState(() {
          currentStatus = status;
          isStatusOpen = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Status updated to $status')),
          );
        }

        AppLogger.info('Mentor status updated to: $status');

        if (status == 'online') {
          _startCallPolling();
        } else {
          _stopCallPolling();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to update status: ${response.statusCode}')),
          );
        }
        AppLogger.error('Failed to update mentor status: ${response.statusCode}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating status: $e')),
        );
      }
      AppLogger.error('Error updating mentor status: $e');
    } finally {
      setState(() => isSessionLoading = false);
    }
  }

  void _startCallPolling() {
    if (pollingTimer != null || mentorId == null) return;
    pollingTimer = Timer.periodic(const Duration(seconds: 5), (_) async {
      try {
        final response = await http.get(Uri.parse('$apiBaseUrl/mentor/poll?mentor_id=$mentorId'));
        final data = jsonDecode(response.body);
        if (data['incoming_call'] && data['room_name'] != null && data['student_id'] != null && incomingCall == null && !showIncomingCall) {
          setState(() {
            incomingCall = {
              'room_name': data['room_name'],
              'student_id': data['student_id'],
              'timestamp': data['timestamp'],
            };
            showIncomingCall = true;
          });
          // Play ringtone
          await ringtonePlayer.play(AssetSource('audio/phone-ringtone-cabinet-356927.mp3'), volume: 1.0);
        }
      } catch (e) {
        AppLogger.error('Polling error: $e');
      }
    });
  }

  void _stopCallPolling() {
    pollingTimer?.cancel();
    pollingTimer = null;
  }

  Future<void> _handleAcceptCall() async {
    if (incomingCall == null || isCallAccepting || mentorId == null || authToken == null) return;

    await ringtonePlayer.stop();
    setState(() => isCallAccepting = true);

    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/call/accept'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'mentor_id': mentorId,
          'room_name': incomingCall!['room_name'],
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final callData = {...incomingCall!, 'token': data['token']};
        setState(() {
          showIncomingCall = false;
          incomingCall = null;
        });

        AppLogger.info('Call accepted successfully');

        // Navigate to IncomingCall widget
        if (mounted) {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => IncomingCall(
                mentorId: mentorId!,
                incomingCall: callData,
                showVideoCall: true,
                onCallStatusChange: _handleCallStatusChange,
              ),
            ),
          );
        }
      } else {
        AppLogger.error('Failed to accept call: ${response.statusCode}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to accept call: ${response.statusCode}')),
          );
        }
      }
    } catch (e) {
      AppLogger.error('Error accepting call: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error accepting call: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isCallAccepting = false);
      }
    }
  }

  Future<void> _handleRejectCall() async {
    if (incomingCall == null) return;

    await ringtonePlayer.stop();

    try {
      await http.post(
        Uri.parse('$apiBaseUrl/call/end'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'room_name': incomingCall!['room_name'],
          'mentor_id': mentorId,
        }),
      );
      AppLogger.info('Call rejected successfully');
    } catch (e) {
      AppLogger.error('Error rejecting call: $e');
    }

    setState(() {
      incomingCall = null;
      showIncomingCall = false;
    });
  }

  void _handleCallStatusChange(String status, Map<String, dynamic>? callData) {
    if (status == 'ended' || status == 'declined' || status == 'error') {
      setState(() {
        incomingCall = null;
        showIncomingCall = false;
        isCallAccepting = false;
      });
    }
  }

  @override
  void dispose() {
    _stopCallPolling();
    ringtonePlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (errorMessage != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(errorMessage!, style: const TextStyle(color: Colors.red)),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadMentorData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Stack(
      children: [
        Scaffold(
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Welcome, Mentor', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                      if (currentStatus == 'online')
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(color: Colors.green[100], borderRadius: BorderRadius.circular(4)),
                          child: const Text('Ready for calls', style: TextStyle(color: Colors.green)),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Profile Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 30,
                                child: Text(mentorData?['mendor']?['first_name']?.substring(0,1) ?? 'M'),
                              ),
                              const SizedBox(width: 16),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('${mentorData?['mendor']?['first_name'] ?? 'Mentor'} ${mentorData?['mendor']?['last_name'] ?? ''}'),
                                  const Text('Senior Mentor'),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          DropdownButton<String>(
                            value: currentStatus,
                            items: statusOptions.map((opt) => DropdownMenuItem<String>(
                              value: opt['value'] as String,
                              child: Text(opt['label'] as String),
                            )).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                _handleStatusChange(value);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Stats Grid
                  GridView.count(
                    crossAxisCount: 2,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    children: const [
                      Card(child: Center(child: Text('Students: 42'))),
                      Card(child: Center(child: Text('Courses: 3'))),
                      Card(child: Center(child: Text('Sessions: 18'))),
                      Card(child: Center(child: Text('Rating: 4.8'))),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Course Details
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Course Information', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          Text('Current Course: ${mentorData?['mendor']?['course_name'] ?? 'N/A'}'),
                          Text('Subject: ${mentorData?['mendor']?['subject_name'] ?? 'N/A'}'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Student Progress
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Student Progress', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          ...studentProgress.map((student) => ListTile(
                                leading: CircleAvatar(backgroundColor: student['avatarColor'] as Color, child: Text(student['name'].toString().substring(0, 1))),
                                title: Text(student['name'].toString()),
                                subtitle: LinearProgressIndicator(value: (student['progress'] as int) / 100),
                              )),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Upcoming Sessions
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Upcoming Sessions', style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          ...upcomingSessions.map((session) => ListTile(
                                title: Text(session['title'].toString()),
                                subtitle: Text(session['time'].toString()),
                              )),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (showIncomingCall && incomingCall != null)
          Positioned.fill(
            child: Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: AlertDialog(
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('Incoming Call', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                      Text('Student ID: ${incomingCall!['student_id']}'),
                      Text('Room: ${incomingCall!['room_name']}'),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: _handleRejectCall,
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                            child: const Text('Decline'),
                          ),
                          ElevatedButton(
                            onPressed: isCallAccepting ? null : _handleAcceptCall,
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                            child: isCallAccepting ? const CircularProgressIndicator() : const Text('Accept'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}